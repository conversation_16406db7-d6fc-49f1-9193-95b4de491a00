# Voice AI Testing Framework - Environment Variables
# Copy this file to .env.local for local development

# =============================================================================
# DEVELOPMENT ENVIRONMENT VARIABLES (for local development only)
# =============================================================================

# For local development, you can use VITE_ prefixed variables
# These will be exposed to the client-side code
VITE_DEEPGRAM_API_KEY=your_deepgram_api_key_here
VITE_OPENAI_API_KEY=sk-your_openai_api_key_here
VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# API Base URL for local development
VITE_API_BASE_URL=http://localhost:8888/.netlify/functions

# Environment
VITE_ENVIRONMENT=development

# =============================================================================
# PRODUCTION ENVIRONMENT VARIABLES (for Netlify deployment)
# =============================================================================

# For production, set these in your Netlify dashboard under Site Settings > Environment Variables
# These are server-side only and will NOT be exposed to the client

# Server-side API keys (used by Netlify Functions)
DEEPGRAM_API_KEY=your_deepgram_api_key_here
OPENAI_API_KEY=sk-your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Additional provider API keys for future expansion
GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AZURE_SPEECH_API_KEY=your_azure_speech_api_key_here
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# Security Note:
# - NEVER commit .env.local to version control
# - For production: Set environment variables in Netlify dashboard
# - Server-side variables (without VITE_ prefix) are secure and not exposed to clients
# - Client-side variables (with VITE_ prefix) are exposed in the browser bundle
