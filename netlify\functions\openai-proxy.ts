import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HandlerContext } from '@netlify/functions';

interface OpenAIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  temperature?: number;
  max_tokens?: number;
  tools?: any[];
}

interface OpenAITTSRequest {
  input: string;
  model?: string;
  voice?: string;
  response_format?: string;
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    // Get API key from environment variables
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const requestBody = JSON.parse(event.body || '{}');
    const endpoint = event.path.includes('/tts') ? 'audio/speech' : 'chat/completions';
    
    // Prepare OpenAI API request
    const openaiUrl = `https://api.openai.com/v1/${endpoint}`;
    
    let requestData;
    let responseHeaders = headers;

    if (endpoint === 'audio/speech') {
      // TTS request
      const ttsRequest: OpenAITTSRequest = requestBody;
      requestData = {
        input: ttsRequest.input,
        model: ttsRequest.model || 'tts-1',
        voice: ttsRequest.voice || 'alloy',
        response_format: ttsRequest.response_format || 'mp3',
      };
      responseHeaders = {
        ...headers,
        'Content-Type': 'audio/mpeg',
      };
    } else {
      // Chat completions request
      const chatRequest: OpenAIRequest = requestBody;
      requestData = {
        model: chatRequest.model || 'gpt-4',
        messages: chatRequest.messages,
        temperature: chatRequest.temperature || 0.7,
        max_tokens: chatRequest.max_tokens || 1000,
        ...(chatRequest.tools && { tools: chatRequest.tools }),
      };
    }

    // Make request to OpenAI
    const response = await fetch(openaiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    if (endpoint === 'audio/speech') {
      // Return audio data as base64
      const audioBuffer = await response.arrayBuffer();
      const base64Audio = Buffer.from(audioBuffer).toString('base64');
      
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ audio: base64Audio }),
      };
    } else {
      // Return JSON response
      const result = await response.json();
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(result),
      };
    }

  } catch (error) {
    console.error('OpenAI proxy error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
    };
  }
};

export { handler };
