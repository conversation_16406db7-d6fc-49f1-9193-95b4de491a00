import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Star, 
  Sparkles, 
  Filter, 
  Info, 
  Mic, 
  MessageSquare, 
  Volume2,
  Crown,
  Zap,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import ModelRegistryService, { ModelInfo, VoiceInfo } from '@/services/modelRegistryService';

interface ModelSelectorProps {
  category: 'stt' | 'llm' | 'tts';
  selectedProvider?: string;
  selectedModel?: string;
  selectedVoice?: string;
  onProviderChange: (provider: string) => void;
  onModelChange: (model: string) => void;
  onVoiceChange?: (voice: string) => void;
  className?: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  category,
  selectedProvider,
  selectedModel,
  selectedVoice,
  onProviderChange,
  onModelChange,
  onVoiceChange,
  className
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showOnlyRecommended, setShowOnlyRecommended] = useState(false);
  const [showOnlyNew, setShowOnlyNew] = useState(false);
  
  const modelRegistry = ModelRegistryService.getInstance();

  const categoryConfig = {
    stt: {
      title: 'Speech-to-Text Models',
      icon: Mic,
      description: 'Convert speech to text with high accuracy'
    },
    llm: {
      title: 'Language Models',
      icon: MessageSquare,
      description: 'Generate intelligent responses and reasoning'
    },
    tts: {
      title: 'Text-to-Speech Models',
      icon: Volume2,
      description: 'Convert text to natural-sounding speech'
    }
  };

  const config = categoryConfig[category];

  // Get available providers and models
  const availableProviders = modelRegistry.getAvailableProviders(category);
  
  const filteredModels = useMemo(() => {
    let models: ModelInfo[] = [];
    
    switch (category) {
      case 'stt':
        models = modelRegistry.getSTTModels();
        break;
      case 'llm':
        models = modelRegistry.getLLMModels();
        break;
      case 'tts':
        models = modelRegistry.getTTSModels();
        break;
    }

    // Filter by provider if selected
    if (selectedProvider) {
      models = models.filter(model => model.provider === selectedProvider);
    }

    // Filter by search term
    if (searchTerm) {
      models = models.filter(model => 
        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.provider.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by recommended
    if (showOnlyRecommended) {
      models = models.filter(model => model.isRecommended);
    }

    // Filter by new
    if (showOnlyNew) {
      models = models.filter(model => model.isNew);
    }

    return models;
  }, [category, selectedProvider, searchTerm, showOnlyRecommended, showOnlyNew, modelRegistry]);

  const availableVoices = useMemo(() => {
    if (category !== 'tts' || !selectedProvider) return [];
    return modelRegistry.getVoicesByProvider(selectedProvider);
  }, [category, selectedProvider, modelRegistry]);

  const getPricingBadgeColor = (pricing?: string) => {
    switch (pricing) {
      case 'free': return 'bg-green-100 text-green-800 border-green-200';
      case 'paid': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'premium': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderModelCard = (model: ModelInfo) => (
    <Card 
      key={model.id}
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        selectedModel === model.id 
          ? "ring-2 ring-blue-500 bg-blue-50" 
          : "hover:bg-gray-50"
      )}
      onClick={() => onModelChange(model.id)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-sm">{model.name}</h3>
            {model.isRecommended && (
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
            )}
            {model.isNew && (
              <Sparkles className="w-4 h-4 text-blue-500" />
            )}
            {model.deprecated && (
              <Badge variant="outline" className="text-xs">Deprecated</Badge>
            )}
          </div>
          <Badge className={cn("text-xs", getPricingBadgeColor(model.pricing))}>
            {model.pricing || 'unknown'}
          </Badge>
        </div>
        
        <p className="text-xs text-gray-600 mb-3">{model.description}</p>
        
        <div className="space-y-2">
          {model.capabilities && model.capabilities.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {model.capabilities.slice(0, 3).map((capability) => (
                <Badge key={capability} variant="secondary" className="text-xs">
                  {capability}
                </Badge>
              ))}
              {model.capabilities.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{model.capabilities.length - 3} more
                </Badge>
              )}
            </div>
          )}
          
          {model.languages && model.languages.length > 0 && (
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Globe className="w-3 h-3" />
              <span>{model.languages.length} languages</span>
            </div>
          )}
          
          {(model.maxTokens || model.contextWindow) && (
            <div className="text-xs text-gray-500">
              {model.maxTokens && <span>Max tokens: {model.maxTokens.toLocaleString()}</span>}
              {model.maxTokens && model.contextWindow && <span> • </span>}
              {model.contextWindow && <span>Context: {model.contextWindow.toLocaleString()}</span>}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderVoiceCard = (voice: VoiceInfo) => (
    <Card 
      key={voice.id}
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        selectedVoice === voice.id 
          ? "ring-2 ring-blue-500 bg-blue-50" 
          : "hover:bg-gray-50"
      )}
      onClick={() => onVoiceChange?.(voice.id)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-sm">{voice.name}</h3>
            {voice.isRecommended && (
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
            )}
            {voice.isNew && (
              <Sparkles className="w-4 h-4 text-blue-500" />
            )}
          </div>
          <Badge className={cn(
            "text-xs",
            voice.category === 'premium' 
              ? "bg-purple-100 text-purple-800 border-purple-200"
              : "bg-blue-100 text-blue-800 border-blue-200"
          )}>
            {voice.category || 'standard'}
          </Badge>
        </div>
        
        <p className="text-xs text-gray-600 mb-2">{voice.description}</p>
        
        <div className="flex items-center gap-4 text-xs text-gray-500">
          <span className="capitalize">{voice.gender}</span>
          {voice.accent && <span>{voice.accent}</span>}
          <span>{voice.language.toUpperCase()}</span>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <config.icon className="w-5 h-5" />
          {config.title}
        </CardTitle>
        <p className="text-sm text-gray-600">{config.description}</p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Provider Selection */}
        <div className="space-y-2">
          <Label>Provider</Label>
          <Select value={selectedProvider} onValueChange={onProviderChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider" />
            </SelectTrigger>
            <SelectContent>
              {availableProviders.map((provider) => (
                <SelectItem key={provider} value={provider}>
                  <span className="capitalize">{provider}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedProvider && (
          <>
            {/* Search and Filters */}
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search models..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant={showOnlyRecommended ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowOnlyRecommended(!showOnlyRecommended)}
                  className="text-xs"
                >
                  <Star className="w-3 h-3 mr-1" />
                  Recommended
                </Button>
                <Button
                  variant={showOnlyNew ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowOnlyNew(!showOnlyNew)}
                  className="text-xs"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  New
                </Button>
              </div>
            </div>

            {/* Models Grid */}
            <div className="space-y-3">
              <Label>Available Models ({filteredModels.length})</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                {filteredModels.map(renderModelCard)}
              </div>
            </div>

            {/* Voice Selection for TTS */}
            {category === 'tts' && availableVoices.length > 0 && (
              <div className="space-y-3">
                <Label>Available Voices ({availableVoices.length})</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-64 overflow-y-auto">
                  {availableVoices.map(renderVoiceCard)}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ModelSelector;
