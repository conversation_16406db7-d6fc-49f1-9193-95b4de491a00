
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, Download, FileText, Volume2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LogEntry {
  id: string;
  timestamp: string;
  inputAudio: Blob | null;
  transcript: string;
  gptResponse: string;
  ttsAudio: Blob | null;
  status: string;
}

interface LogViewerProps {
  sessionLogs: LogEntry[];
  currentSession: string | null;
}

const LogViewer: React.FC<LogViewerProps> = ({
  sessionLogs,
  currentSession
}) => {
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  const currentLog = selectedLog || (currentSession ? sessionLogs.find(log => log.id === currentSession) : sessionLogs[0]);

  const playAudio = (audioBlob: Blob | null, type: string) => {
    if (!audioBlob) return;
    
    const audio = new Audio(URL.createObjectURL(audioBlob));
    setPlayingAudio(type);
    
    audio.onended = () => setPlayingAudio(null);
    audio.play();
  };

  const downloadFile = (content: string | Blob, filename: string, type: string) => {
    const blob = typeof content === 'string' 
      ? new Blob([content], { type: 'text/plain' })
      : content;
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!currentLog) {
    return (
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-8 text-center">
          <FileText className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <div className="text-gray-400">No session logs available</div>
          <div className="text-sm text-gray-500 mt-2">
            Start recording to generate your first test session
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Session Selector */}
      {sessionLogs.length > 1 && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Recent Sessions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {sessionLogs.slice(0, 5).map((log) => (
                <Button
                  key={log.id}
                  variant={selectedLog?.id === log.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedLog(log)}
                  className="text-xs"
                >
                  {new Date(log.timestamp).toLocaleTimeString()}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Log View */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-lg">Session Details</CardTitle>
            <div className="text-sm text-gray-400 mt-1">
              {new Date(currentLog.timestamp).toLocaleString()}
            </div>
          </div>
          <Badge variant={currentLog.status === 'completed' ? 'default' : 'secondary'}>
            {currentLog.status}
          </Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Audio */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold flex items-center gap-2">
                <Volume2 className="w-4 h-4" />
                Input Audio
              </h4>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => playAudio(currentLog.inputAudio, 'input')}
                  disabled={!currentLog.inputAudio}
                >
                  {playingAudio === 'input' ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => currentLog.inputAudio && downloadFile(currentLog.inputAudio, 'input_audio.mp3', 'audio/mp3')}
                  disabled={!currentLog.inputAudio}
                >
                  <Download className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div className="p-3 bg-gray-900 rounded border border-gray-600">
              {currentLog.inputAudio ? (
                <div className="text-sm text-gray-400">
                  Audio recorded • {(currentLog.inputAudio.size / 1024).toFixed(1)}KB
                </div>
              ) : (
                <div className="text-sm text-gray-500">No audio data</div>
              )}
            </div>
          </div>

          {/* STT Transcript */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">STT Transcript (Deepgram)</h4>
              <Button
                size="sm"
                variant="outline"
                onClick={() => downloadFile(currentLog.transcript, 'transcript.txt', 'text/plain')}
              >
                <Download className="w-3 h-3" />
              </Button>
            </div>
            <div className="p-4 bg-gray-900 rounded border border-gray-600">
              <div className="text-sm whitespace-pre-wrap">
                {currentLog.transcript || 'No transcript available'}
              </div>
            </div>
          </div>

          {/* GPT Response */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">GPT-4 Response</h4>
              <Button
                size="sm"
                variant="outline"
                onClick={() => downloadFile(currentLog.gptResponse, 'gpt_response.txt', 'text/plain')}
              >
                <Download className="w-3 h-3" />
              </Button>
            </div>
            <div className="p-4 bg-gray-900 rounded border border-gray-600">
              <div className="text-sm whitespace-pre-wrap">
                {currentLog.gptResponse || 'No response available'}
              </div>
            </div>
          </div>

          {/* TTS Output */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold flex items-center gap-2">
                <Volume2 className="w-4 h-4" />
                TTS Output (ElevenLabs)
              </h4>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => playAudio(currentLog.ttsAudio, 'output')}
                  disabled={!currentLog.ttsAudio}
                >
                  {playingAudio === 'output' ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => currentLog.ttsAudio && downloadFile(currentLog.ttsAudio, 'tts_output.mp3', 'audio/mp3')}
                  disabled={!currentLog.ttsAudio}
                >
                  <Download className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div className="p-3 bg-gray-900 rounded border border-gray-600">
              {currentLog.ttsAudio ? (
                <div className="text-sm text-gray-400">
                  Audio generated • {(currentLog.ttsAudio.size / 1024).toFixed(1)}KB
                </div>
              ) : (
                <div className="text-sm text-gray-500">No TTS audio available</div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LogViewer;
