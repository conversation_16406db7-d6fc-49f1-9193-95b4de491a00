
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Download, Trash2 } from 'lucide-react';

interface SessionHistoryProps {
  sessions: any[];
  onSessionSelect: (session: any) => void;
}

const SessionHistory: React.FC<SessionHistoryProps> = ({
  sessions,
  onSessionSelect
}) => {
  if (sessions.length === 0) {
    return (
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-8 text-center">
          <Clock className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <div className="text-gray-400">No session history</div>
          <div className="text-sm text-gray-500 mt-2">
            Previous sessions will appear here
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Session History</CardTitle>
        <Badge variant="outline">{sessions.length} sessions</Badge>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sessions.map((session, index) => (
            <div 
              key={session.id}
              className="flex items-center justify-between p-3 bg-gray-900 rounded border border-gray-600 hover:border-gray-500 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                  {index + 1}
                </div>
                <div>
                  <div className="font-medium">
                    {new Date(session.timestamp).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400">
                    Status: {session.status} • Duration: ~5.5s
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSessionSelect(session)}
                >
                  View
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-400 hover:text-red-300"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {sessions.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-700">
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Export All Sessions
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SessionHistory;
