
import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Mic, Square, Play, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ModelSelector from './ModelSelector';

const STTTester = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState('');
  const [provider, setProvider] = useState('deepgram');
  const [model, setModel] = useState('nova-2');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();



  const handleStartRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      toast({
        title: "Recording Error",
        description: "Could not access microphone",
        variant: "destructive"
      });
    }
  };

  const handleStopRecording = () => {
    setIsRecording(false);
  };

  const handleTranscribe = async () => {
    if (!audioBlob) return;
    
    setIsProcessing(true);
    // Simulate transcription process
    setTimeout(() => {
      setTranscript("This is a sample transcription from " + provider + " using " + model + " model.");
      setIsProcessing(false);
      toast({
        title: "Transcription Complete",
        description: `Processed with ${provider} (${model})`
      });
    }, 2000);
  };

  const handlePlayAudio = () => {
    if (audioBlob) {
      const audio = new Audio(URL.createObjectURL(audioBlob));
      audio.play();
    }
  };

  const handleDownloadAudio = () => {
    if (audioBlob) {
      const url = URL.createObjectURL(audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `stt-test-${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-2xl text-blue-900">Speech-to-Text Testing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Model Selection */}
          <ModelSelector
            category="stt"
            selectedProvider={provider}
            selectedModel={model}
            onProviderChange={setProvider}
            onModelChange={setModel}
          />

          {/* Recording Controls */}
          <div className="flex items-center justify-center space-x-4">
            <Button
              size="lg"
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              className={isRecording ? "bg-red-500 hover:bg-red-600" : "bg-blue-500 hover:bg-blue-600"}
            >
              {isRecording ? <Square className="w-5 h-5 mr-2" /> : <Mic className="w-5 h-5 mr-2" />}
              {isRecording ? 'Stop Recording' : 'Start Recording'}
            </Button>
            
            {audioBlob && (
              <>
                <Button variant="outline" onClick={handlePlayAudio}>
                  <Play className="w-4 h-4 mr-2" />
                  Play
                </Button>
                <Button variant="outline" onClick={handleDownloadAudio}>
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                <Button 
                  onClick={handleTranscribe}
                  disabled={isProcessing}
                  className="bg-green-500 hover:bg-green-600"
                >
                  {isProcessing ? 'Processing...' : 'Transcribe'}
                </Button>
              </>
            )}
          </div>

          {/* Transcript Output */}
          {transcript && (
            <div className="space-y-2">
              <Label>Transcription Result</Label>
              <Textarea
                value={transcript}
                readOnly
                className="min-h-[100px] bg-white"
                placeholder="Transcription will appear here..."
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default STTTester;
