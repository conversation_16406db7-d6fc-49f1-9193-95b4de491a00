import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, createMockTool } from '@/test/utils';
import ToolsManager from '../ToolsManager';
import type { ToolsManagerProps } from '@/types';

describe('ToolsManager', () => {
  const mockTools = [
    createMockTool({
      id: 'tool-1',
      name: 'Email Tool',
      description: 'Send emails',
      enabled: true,
    }),
    createMockTool({
      id: 'tool-2',
      name: 'Calendar Tool',
      description: 'Manage calendar events',
      enabled: false,
    }),
  ];

  const defaultProps: ToolsManagerProps = {
    tools: mockTools,
    onToolsChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render tools manager component', () => {
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    expect(screen.getByText('Tools Management')).toBeInTheDocument();
    expect(screen.getByText('Email Tool')).toBeInTheDocument();
    expect(screen.getByText('Calendar Tool')).toBeInTheDocument();
  });

  it('should show add tool button', () => {
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    const addButton = screen.getByText(/add.*tool/i);
    expect(addButton).toBeInTheDocument();
  });

  it('should display tool information correctly', () => {
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    expect(screen.getByText('Email Tool')).toBeInTheDocument();
    expect(screen.getByText('Send emails')).toBeInTheDocument();
    expect(screen.getByText('https://example.com/webhook')).toBeInTheDocument();
  });

  it('should show enabled/disabled status for tools', () => {
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Should show enabled status for first tool
    const enabledBadge = screen.getByText('Enabled');
    expect(enabledBadge).toBeInTheDocument();
    
    // Should show disabled status for second tool
    const disabledBadge = screen.getByText('Disabled');
    expect(disabledBadge).toBeInTheDocument();
  });

  it('should open add tool form when add button is clicked', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Should show form fields
    expect(screen.getByLabelText(/tool name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/webhook url/i)).toBeInTheDocument();
  });

  it('should validate tool form inputs', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Open add form
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Try to save without filling required fields
    const saveButton = screen.getByText(/save/i);
    await user.click(saveButton);
    
    // Should show validation errors
    expect(screen.getByText(/tool name.*required/i)).toBeInTheDocument();
  });

  it('should add new tool when form is submitted correctly', async () => {
    const user = userEvent.setup();
    const onToolsChange = vi.fn();
    
    renderWithProviders(
      <ToolsManager {...defaultProps} onToolsChange={onToolsChange} />
    );
    
    // Open add form
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Fill form
    await user.type(screen.getByLabelText(/tool name/i), 'New Tool');
    await user.type(screen.getByLabelText(/description/i), 'A new tool');
    await user.type(screen.getByLabelText(/webhook url/i), 'https://example.com/new-webhook');
    
    // Save
    const saveButton = screen.getByText(/save/i);
    await user.click(saveButton);
    
    // Should call onToolsChange with new tool
    expect(onToolsChange).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          name: 'New Tool',
          description: 'A new tool',
          webhookUrl: 'https://example.com/new-webhook',
        }),
      ])
    );
  });

  it('should toggle tool enabled status', async () => {
    const user = userEvent.setup();
    const onToolsChange = vi.fn();
    
    renderWithProviders(
      <ToolsManager {...defaultProps} onToolsChange={onToolsChange} />
    );
    
    // Find and click toggle for first tool
    const toggleButton = screen.getAllByRole('switch')[0];
    await user.click(toggleButton);
    
    // Should call onToolsChange with updated tool
    expect(onToolsChange).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          id: 'tool-1',
          enabled: false, // Should be toggled
        }),
      ])
    );
  });

  it('should delete tool when delete button is clicked', async () => {
    const user = userEvent.setup();
    const onToolsChange = vi.fn();
    
    renderWithProviders(
      <ToolsManager {...defaultProps} onToolsChange={onToolsChange} />
    );
    
    // Find and click delete button for first tool
    const deleteButtons = screen.getAllByText(/delete/i);
    await user.click(deleteButtons[0]);
    
    // Should call onToolsChange without the deleted tool
    expect(onToolsChange).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          id: 'tool-2', // Only second tool should remain
        }),
      ])
    );
  });

  it('should edit existing tool', async () => {
    const user = userEvent.setup();
    const onToolsChange = vi.fn();
    
    renderWithProviders(
      <ToolsManager {...defaultProps} onToolsChange={onToolsChange} />
    );
    
    // Find and click edit button for first tool
    const editButtons = screen.getAllByText(/edit/i);
    await user.click(editButtons[0]);
    
    // Should populate form with existing values
    expect(screen.getByDisplayValue('Email Tool')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Send emails')).toBeInTheDocument();
    
    // Modify the name
    const nameInput = screen.getByDisplayValue('Email Tool');
    await user.clear(nameInput);
    await user.type(nameInput, 'Updated Email Tool');
    
    // Save changes
    const saveButton = screen.getByText(/save/i);
    await user.click(saveButton);
    
    // Should call onToolsChange with updated tool
    expect(onToolsChange).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          id: 'tool-1',
          name: 'Updated Email Tool',
        }),
      ])
    );
  });

  it('should test tool webhook', async () => {
    const user = userEvent.setup();
    
    // Mock fetch for webhook test
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
    });
    
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Find and click test button for first tool
    const testButtons = screen.getAllByText(/test/i);
    await user.click(testButtons[0]);
    
    // Should make fetch request to webhook URL
    expect(global.fetch).toHaveBeenCalledWith(
      'https://example.com/webhook',
      expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
    );
  });

  it('should handle webhook test failure', async () => {
    const user = userEvent.setup();
    
    // Mock fetch to fail
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Find and click test button for first tool
    const testButtons = screen.getAllByText(/test/i);
    await user.click(testButtons[0]);
    
    // Should handle error gracefully
    await waitFor(() => {
      expect(screen.getByText(/test failed/i)).toBeInTheDocument();
    });
  });

  it('should show empty state when no tools', () => {
    renderWithProviders(
      <ToolsManager tools={[]} onToolsChange={vi.fn()} />
    );
    
    expect(screen.getByText(/no tools configured/i)).toBeInTheDocument();
  });

  it('should validate webhook URL format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Open add form
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Enter invalid URL
    await user.type(screen.getByLabelText(/webhook url/i), 'invalid-url');
    
    // Try to save
    const saveButton = screen.getByText(/save/i);
    await user.click(saveButton);
    
    // Should show validation error
    expect(screen.getByText(/invalid.*url/i)).toBeInTheDocument();
  });

  it('should show tool parameters configuration', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Open add form
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Should show parameters section
    expect(screen.getByText(/parameters/i)).toBeInTheDocument();
  });

  it('should cancel form editing', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ToolsManager {...defaultProps} />);
    
    // Open add form
    const addButton = screen.getByText(/add.*tool/i);
    await user.click(addButton);
    
    // Fill some data
    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    
    // Cancel
    const cancelButton = screen.getByText(/cancel/i);
    await user.click(cancelButton);
    
    // Form should be closed
    expect(screen.queryByLabelText(/tool name/i)).not.toBeInTheDocument();
  });
});
