[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# API proxy functions
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.deepgram.com https://api.openai.com https://api.elevenlabs.io https://speech.googleapis.com; media-src 'self' blob:; worker-src 'self' blob:;"

# Environment variables for different deploy contexts
[context.production.environment]
  VITE_ENVIRONMENT = "production"
  VITE_API_BASE_URL = "/.netlify/functions"

[context.deploy-preview.environment]
  VITE_ENVIRONMENT = "staging"
  VITE_API_BASE_URL = "/.netlify/functions"

[context.branch-deploy.environment]
  VITE_ENVIRONMENT = "development"
  VITE_API_BASE_URL = "/.netlify/functions"
