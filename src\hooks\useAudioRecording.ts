import { useState, useRef, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import ErrorService, { ErrorCode } from '@/services/errorService';
import type { UseAudioRecorderReturn, AudioConfig } from '@/types';

interface UseAudioRecordingOptions {
  config?: AudioConfig;
  onRecordingStart?: () => void;
  onRecordingStop?: (audioBlob: Blob) => void;
  onError?: (error: string) => void;
}

export const useAudioRecording = (options: UseAudioRecordingOptions = {}): UseAudioRecorderReturn => {
  const { config, onRecordingStart, onRecordingStop, onError } = options;
  const { toast } = useToast();
  const errorService = ErrorService.getInstance();

  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  const startDurationTimer = useCallback(() => {
    const startTime = Date.now();
    durationIntervalRef.current = setInterval(() => {
      setDuration(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);
  }, []);

  const stopDurationTimer = useCallback(() => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    setDuration(0);
  }, []);

  const monitorAudioLevel = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

    const updateLevel = () => {
      if (!analyserRef.current || !isRecording) {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
        return;
      }

      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;
      setAudioLevel(average / 255);

      animationFrameRef.current = requestAnimationFrame(updateLevel);
    };

    updateLevel();
  }, [isRecording]);

  const cleanup = useCallback(() => {
    // Stop media recorder
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // Close audio context
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
    }

    // Stop media stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Clear timers and animation frames
    stopDurationTimer();
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Reset state
    setAudioLevel(0);
    setIsRecording(false);
  }, [stopDurationTimer]);

  const startRecording = useCallback(async (): Promise<void> => {
    try {
      setError(null);

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: config?.sampleRate || 44100,
          channelCount: config?.channels || 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      streamRef.current = stream;

      // Set up audio analysis for visual feedback
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;

      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);

      // Set up media recorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      mediaRecorderRef.current = mediaRecorder;

      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        onRecordingStop?.(blob);
        cleanup();
      };

      mediaRecorder.onerror = (event) => {
        const errorMessage = 'Recording failed';
        setError(errorMessage);
        onError?.(errorMessage);
        cleanup();
        
        toast({
          title: "Recording Error",
          description: errorMessage,
          variant: "destructive"
        });
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      setIsRecording(true);
      startDurationTimer();
      monitorAudioLevel();
      onRecordingStart?.();

      toast({
        title: "Recording Started",
        description: "Speak into your microphone"
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to access microphone';
      setError(errorMessage);
      onError?.(errorMessage);

      // Handle specific error types
      let errorCode = ErrorCode.AUDIO_RECORDING_FAILED;
      if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
        errorCode = ErrorCode.MICROPHONE_ACCESS_DENIED;
      }

      errorService.handleError(errorService.createError(
        errorCode,
        errorMessage,
        { originalError: err },
        { component: 'useAudioRecording', action: 'startRecording' }
      ));

      toast({
        title: "Recording Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [config, onRecordingStart, onRecordingStop, onError, toast, startDurationTimer, monitorAudioLevel, cleanup]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    } else {
      cleanup();
    }
  }, [cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Monitor recording state changes
  useEffect(() => {
    if (isRecording) {
      monitorAudioLevel();
    } else {
      setAudioLevel(0);
    }
  }, [isRecording, monitorAudioLevel]);

  return {
    isRecording,
    audioLevel,
    duration,
    startRecording,
    stopRecording,
    audioBlob,
    error
  };
};
