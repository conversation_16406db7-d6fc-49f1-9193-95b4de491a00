import { <PERSON><PERSON>, <PERSON><PERSON>Event, HandlerContext } from '@netlify/functions';

interface DeepgramRequest {
  audio: string; // base64 encoded audio
  model?: string;
  language?: string;
  enhanced?: boolean;
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    // Get API key from environment variables
    const apiKey = process.env.DEEPGRAM_API_KEY;
    if (!apiKey) {
      throw new Error('Deepgram API key not configured');
    }

    const requestBody: DeepgramRequest = JSON.parse(event.body || '{}');
    
    if (!requestBody.audio) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Audio data is required' }),
      };
    }

    // Convert base64 to buffer
    const audioBuffer = Buffer.from(requestBody.audio, 'base64');

    // Prepare Deepgram API request
    const deepgramUrl = new URL('https://api.deepgram.com/v1/listen');
    
    // Add query parameters
    const params = new URLSearchParams({
      model: requestBody.model || 'nova-2',
      language: requestBody.language || 'en',
      punctuate: 'true',
      diarize: 'false',
    });

    if (requestBody.enhanced) {
      params.append('tier', 'enhanced');
    }

    deepgramUrl.search = params.toString();

    // Make request to Deepgram
    const response = await fetch(deepgramUrl.toString(), {
      method: 'POST',
      headers: {
        'Authorization': `Token ${apiKey}`,
        'Content-Type': 'audio/wav',
      },
      body: audioBuffer,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Deepgram API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(result),
    };

  } catch (error) {
    console.error('Deepgram proxy error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
    };
  }
};

export { handler };
