# Deployment Guide - Voice AI Sandbox

This guide covers deploying your Voice AI Sandbox to Netlify with secure API key management and comprehensive model selection.

## 🚀 Quick Deployment to Netlify

### 1. Prepare Your Repository

1. Ensure all your changes are committed to your Git repository
2. Push your code to GitHub, Git<PERSON>ab, or Bitbucket

### 2. Deploy to Netlify

#### Option A: Netlify Dashboard (Recommended)

1. Go to [Netlify](https://netlify.com) and sign in
2. Click "New site from Git"
3. Connect your Git provider and select your repository
4. Configure build settings:
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`
   - **Node version**: `18` (set in Environment Variables)

#### Option B: Netlify CLI

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy from your project directory
netlify deploy --prod
```

### 3. Configure Environment Variables

In your Netlify dashboard, go to **Site Settings > Environment Variables** and add:

#### Required API Keys (Server-side - Secure)
```
DEEPGRAM_API_KEY=your_deepgram_api_key_here
OPENAI_API_KEY=sk-your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
```

#### Optional API Keys (For Future Expansion)
```
GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AZURE_SPEECH_API_KEY=your_azure_speech_api_key_here
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here
```

#### Build Configuration
```
NODE_VERSION=18
VITE_ENVIRONMENT=production
```

### 4. Enable Netlify Functions

Your `netlify.toml` file is already configured to:
- Route API calls to Netlify Functions (`/api/*` → `/.netlify/functions/*`)
- Handle SPA routing
- Set security headers

## 🔐 Security Architecture

### API Key Management

**✅ Secure (Production)**
- API keys are stored as Netlify environment variables
- Keys are only accessible server-side via Netlify Functions
- Client never sees actual API keys

**❌ Insecure (Avoid)**
- Hard-coding API keys in client code
- Using `VITE_` prefixed environment variables for production

### How It Works

1. **Client** makes request to `/api/deepgram-proxy`
2. **Netlify Function** receives request with your secure API key
3. **Function** calls actual API (Deepgram, OpenAI, etc.)
4. **Function** returns response to client
5. **Client** never sees the actual API key

## 🎯 Model Selection Features

### Comprehensive Model Support

#### Speech-to-Text (STT)
- **Deepgram**: Nova 2, Nova, Enhanced, Base
- **OpenAI**: Whisper
- **AssemblyAI**: Best, Nano
- **Google**: Latest Long, Latest Short, Chirp

#### Language Models (LLM)
- **OpenAI**: GPT-4o, GPT-4 Turbo, GPT-4, GPT-3.5 Turbo
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Opus, Sonnet, Haiku
- **Google**: Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini Pro

#### Text-to-Speech (TTS)
- **ElevenLabs**: Multilingual v2, Turbo v2.5, Turbo v2, Monolingual v1
- **OpenAI**: TTS-1 HD, TTS-1
- **Google**: Neural2, WaveNet, Standard
- **Azure**: Neural, Standard

### Voice Selection
- **ElevenLabs**: 8+ premium voices (Aria, Roger, Sarah, etc.)
- **OpenAI**: 6 voices (Alloy, Echo, Fable, Onyx, Nova, Shimmer)
- **Google/Azure**: Multiple language and accent options

## 🛠 Development vs Production

### Local Development
```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your API keys

# Start development server
npm run dev

# Start Netlify Functions locally (optional)
netlify dev
```

### Production Deployment
- API keys are managed server-side
- All requests go through secure Netlify Functions
- No API keys exposed to client

## 📊 Monitoring and Analytics

### Netlify Analytics
- Enable in Netlify dashboard for usage insights
- Monitor function invocations and performance

### Error Monitoring
- Check Netlify Function logs for API errors
- Monitor client-side errors in browser console

## 🔧 Troubleshooting

### Common Issues

#### 1. API Keys Not Working
- Verify environment variables are set in Netlify dashboard
- Check variable names match exactly (case-sensitive)
- Ensure no extra spaces in API keys

#### 2. Functions Not Deploying
- Check `netlify.toml` configuration
- Verify function files are in `netlify/functions/` directory
- Check build logs for TypeScript compilation errors

#### 3. CORS Errors
- Functions include CORS headers automatically
- Check if you're calling the correct proxy endpoints

#### 4. Model Selection Issues
- Verify ModelRegistryService is properly imported
- Check browser console for JavaScript errors
- Ensure provider names match exactly

### Debug Commands

```bash
# Check build locally
npm run build

# Test functions locally
netlify dev

# Check function logs
netlify functions:list
netlify functions:invoke function-name
```

## 🚀 Performance Optimization

### Caching Strategy
- Static assets cached by Netlify CDN
- API responses can be cached client-side
- Consider implementing request deduplication

### Bundle Optimization
- Vite automatically optimizes bundle size
- Tree-shaking removes unused code
- Consider lazy loading for large components

## 📈 Scaling Considerations

### API Rate Limits
- Monitor usage across all providers
- Implement client-side rate limiting
- Consider caching frequent requests

### Cost Management
- Monitor API usage in provider dashboards
- Set up billing alerts
- Consider implementing usage quotas

## 🔄 Updates and Maintenance

### Adding New Providers
1. Add API keys to Netlify environment variables
2. Create new proxy function in `netlify/functions/`
3. Update `ModelRegistryService` with new models
4. Test thoroughly before deploying

### Model Updates
- Update `ModelRegistryService` with new models
- Test compatibility with existing code
- Deploy and verify functionality

---

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Netlify function logs
3. Check browser console for client-side errors
4. Verify API key configuration

Your Voice AI Sandbox is now ready for production use with secure API key management and comprehensive model selection! 🎉
