
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Send, Brain, Wrench, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Tool } from './ToolsManager';
import { ToolService, ToolCall } from '@/services/toolService';
import ModelSelector from './ModelSelector';

interface LLMTesterProps {
  tools?: Tool[];
}

const LLMTester: React.FC<LLMTesterProps> = ({ tools = [] }) => {
  const [input, setInput] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('You are a helpful AI assistant. Respond naturally and concisely.');
  const [response, setResponse] = useState('');
  const [provider, setProvider] = useState('openai');
  const [model, setModel] = useState('gpt-4');
  const [isProcessing, setIsProcessing] = useState(false);
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const { toast } = useToast();



  const handleSubmit = async () => {
    if (!input.trim()) return;
    
    setIsProcessing(true);
    setToolCalls([]);
    
    // Generate enhanced system prompt with tools
    const toolsPrompt = ToolService.generateToolsPrompt(tools);
    const enhancedSystemPrompt = systemPrompt + toolsPrompt;
    
    // Simulate LLM processing
    setTimeout(async () => {
      let mockResponse = `This is a sample response from ${provider} (${model}) to: "${input}"\n\nSystem prompt used: "${enhancedSystemPrompt}"`;
      
      // Simulate tool calling for demonstration
      if (tools.length > 0 && input.toLowerCase().includes('email')) {
        const emailTool = tools.find(t => t.name.toLowerCase().includes('email') && t.enabled);
        if (emailTool) {
          mockResponse += `\n\nI'll help you with that email request.`;
          mockResponse += `\n\n\`\`\`json
{
  "tool_call": {
    "tool_name": "${emailTool.name}",
    "parameters": {
      "email": "<EMAIL>",
      "subject": "Sample Subject",
      "message": "This is a sample email generated by the AI assistant."
    }
  }
}
\`\`\``;
        }
      }
      
      // Check for tool calls in the response
      const toolCallData = ToolService.parseToolCallFromResponse(mockResponse, tools);
      
      if (toolCallData) {
        // Execute the tool
        const toolCall = await ToolService.executeTool(toolCallData.tool, toolCallData.parameters);
        setToolCalls([toolCall]);
        
        // Remove tool call from response for display
        const cleanResponse = ToolService.removeToolCallFromResponse(mockResponse);
        if (toolCall.status === 'success') {
          setResponse(cleanResponse + `\n\n✅ Tool "${toolCall.toolName}" executed successfully.`);
        } else {
          setResponse(cleanResponse + `\n\n❌ Tool "${toolCall.toolName}" failed: ${toolCall.error}`);
        }
      } else {
        setResponse(mockResponse);
      }
      
      setIsProcessing(false);
      toast({
        title: "Response Generated",
        description: `Processed with ${provider} (${model})${toolCallData ? ' with tool execution' : ''}`
      });
    }, 1500);
  };

  const quickPrompts = [
    "You are a helpful assistant. Be concise and friendly.",
    "You are a technical expert. Provide detailed explanations.",
    "You are a creative writer. Be imaginative and engaging.",
    "You are a customer service representative. Be polite and solution-focused."
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
        <CardHeader>
          <CardTitle className="text-2xl text-purple-900 flex items-center gap-2">
            <Brain className="w-6 h-6" />
            Large Language Model Testing
            {tools.length > 0 && (
              <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1">
                <Wrench className="w-3 h-3" />
                {tools.filter(t => t.enabled).length} Tools
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Model Selection */}
          <ModelSelector
            category="llm"
            selectedProvider={provider}
            selectedModel={model}
            onProviderChange={setProvider}
            onModelChange={setModel}
          />

          {/* System Prompt */}
          <div className="space-y-2">
            <Label>System Prompt</Label>
            <Textarea
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              className="min-h-[80px] bg-white"
            />
            <div className="flex flex-wrap gap-2">
              {quickPrompts.map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setSystemPrompt(prompt)}
                  className="text-xs"
                >
                  Preset {index + 1}
                </Button>
              ))}
            </div>
          </div>

          {/* Available Tools Display */}
          {tools.length > 0 && (
            <div className="space-y-2">
              <Label>Available Tools</Label>
              <div className="flex flex-wrap gap-2">
                {tools.map((tool) => (
                  <Badge 
                    key={tool.id} 
                    variant={tool.enabled ? "default" : "secondary"}
                    className={tool.enabled ? "bg-orange-100 text-orange-800" : ""}
                  >
                    {tool.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="space-y-2">
            <Label>User Input</Label>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Enter your message or paste a transcript here..."
              className="min-h-[100px] bg-white"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleSubmit}
              disabled={isProcessing || !input.trim()}
              size="lg"
              className="bg-purple-500 hover:bg-purple-600"
            >
              <Send className="w-4 h-4 mr-2" />
              {isProcessing ? 'Processing...' : 'Generate Response'}
            </Button>
          </div>

          {/* Tool Calls Log */}
          {toolCalls.length > 0 && (
            <div className="space-y-2">
              <Label>Tool Executions</Label>
              <div className="space-y-2">
                {toolCalls.map((call, index) => (
                  <Card key={index} className="bg-white">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Wrench className="w-4 h-4" />
                          <span className="font-medium">{call.toolName}</span>
                        </div>
                        <Badge 
                          variant={call.status === 'success' ? 'default' : 'destructive'}
                          className="flex items-center gap-1"
                        >
                          {call.status === 'success' ? 
                            <CheckCircle className="w-3 h-3" /> : 
                            <AlertCircle className="w-3 h-3" />
                          }
                          {call.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div><strong>Parameters:</strong> {JSON.stringify(call.parameters, null, 2)}</div>
                        {call.error && <div className="text-red-600"><strong>Error:</strong> {call.error}</div>}
                        {call.response && <div><strong>Response:</strong> {call.response}</div>}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Response */}
          {response && (
            <div className="space-y-2">
              <Label>LLM Response</Label>
              <Textarea
                value={response}
                readOnly
                className="min-h-[150px] bg-white"
                placeholder="Response will appear here..."
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LLMTester;
