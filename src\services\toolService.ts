
import { Tool } from '@/components/ToolsManager';

export interface ToolCall {
  toolId: string;
  toolName: string;
  parameters: Record<string, any>;
  timestamp: string;
  status: 'pending' | 'success' | 'error';
  response?: any;
  error?: string;
}

export class ToolService {
  static async executeTool(tool: Tool, parameters: Record<string, any>): Promise<ToolCall> {
    const toolCall: ToolCall = {
      toolId: tool.id,
      toolName: tool.name,
      parameters,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    try {
      console.log(`Executing tool: ${tool.name}`, { parameters });
      
      const response = await fetch(tool.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(parameters)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.text();
      toolCall.response = responseData;
      toolCall.status = 'success';
      
      console.log(`Tool ${tool.name} executed successfully:`, responseData);
    } catch (error) {
      console.error(`Tool ${tool.name} execution failed:`, error);
      toolCall.error = error instanceof Error ? error.message : 'Unknown error';
      toolCall.status = 'error';
    }

    return toolCall;
  }

  static generateToolsPrompt(tools: Tool[]): string {
    if (tools.length === 0) return '';

    const activTools = tools.filter(tool => tool.enabled);
    if (activTools.length === 0) return '';

    const toolDescriptions = activTools.map(tool => {
      const paramsList = Object.entries(tool.parameters)
        .map(([key, type]) => `${key} (${type})`)
        .join(', ');
      
      return `- ${tool.name}: ${tool.description}. Parameters: {${paramsList}}`;
    }).join('\n');

    return `

You have access to the following tools that you can call when appropriate:
${toolDescriptions}

To call a tool, include a JSON block in your response with this format:
\`\`\`json
{
  "tool_call": {
    "tool_name": "exact_tool_name",
    "parameters": {
      "param1": "value1",
      "param2": "value2"
    }
  }
}
\`\`\`

Only call tools when they are relevant to the user's request. Fill in the parameters based on the context of the conversation.`;
  }

  static parseToolCallFromResponse(response: string, tools: Tool[]): { tool: Tool; parameters: Record<string, any> } | null {
    try {
      // Look for JSON blocks in the response
      const jsonRegex = /```json\s*\n([\s\S]*?)\n```/g;
      let match;
      
      while ((match = jsonRegex.exec(response)) !== null) {
        try {
          const jsonContent = JSON.parse(match[1]);
          
          if (jsonContent.tool_call && jsonContent.tool_call.tool_name) {
            const toolName = jsonContent.tool_call.tool_name;
            const tool = tools.find(t => t.name === toolName && t.enabled);
            
            if (tool) {
              return {
                tool,
                parameters: jsonContent.tool_call.parameters || {}
              };
            }
          }
        } catch (parseError) {
          console.warn('Failed to parse JSON block:', parseError);
        }
      }
    } catch (error) {
      console.warn('Error parsing tool call from response:', error);
    }
    
    return null;
  }

  static removeToolCallFromResponse(response: string): string {
    // Remove JSON tool call blocks from the response
    return response.replace(/```json\s*\n[\s\S]*?\n```/g, '').trim();
  }
}
