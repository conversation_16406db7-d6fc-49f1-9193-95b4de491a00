# Voice AI Testing Framework - Environment Variables
# Copy this file to .env.local and fill in your API keys

# Deepgram API Key for Speech-to-Text
# Get your key from: https://console.deepgram.com/
VITE_DEEPGRAM_API_KEY=your_deepgram_api_key_here

# OpenAI API Key for Language Model
# Get your key from: https://platform.openai.com/api-keys
VITE_OPENAI_API_KEY=sk-your_openai_api_key_here

# ElevenLabs API Key for Text-to-Speech
# Get your key from: https://elevenlabs.io/app/settings/api-keys
VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# API Base URL (optional)
VITE_API_BASE_URL=https://api.example.com

# Environment (development, staging, production)
VITE_ENVIRONMENT=development

# Security Note:
# - Never commit .env.local to version control
# - Use VITE_ prefix for variables that need to be available in the browser
# - Consider using a secure key management service in production
