import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockMediaRecorder, createMockBlob } from '@/test/utils';
import Index from '@/pages/Index';

describe('Voice Pipeline Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock getUserMedia
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: vi.fn().mockResolvedValue({
          getTracks: vi.fn(() => [
            { stop: vi.fn(), kind: 'audio', enabled: true }
          ]),
        }),
      },
      writable: true,
    });
  });

  it('should complete full voice pipeline flow', async () => {
    const user = userEvent.setup();
    const { mockRecorder, simulateDataAvailable, simulateStop } = mockMediaRecorder();
    
    // Mock MediaRecorder
    global.MediaRecorder = vi.fn().mockImplementation(() => mockRecorder);
    
    renderWithProviders(<Index />);
    
    // Should start on full pipeline tab
    expect(screen.getByText('Voice AI Testing Framework')).toBeInTheDocument();
    expect(screen.getByText('Full Pipeline')).toBeInTheDocument();
    
    // Find and click record button
    const recordButton = screen.getByRole('button', { name: /start recording|record/i });
    expect(recordButton).toBeInTheDocument();
    
    await user.click(recordButton);
    
    // Should show recording state
    await waitFor(() => {
      expect(screen.getByText(/recording/i)).toBeInTheDocument();
    });
    
    // Simulate audio data and stop recording
    const mockAudioBlob = createMockBlob();
    simulateDataAvailable(mockAudioBlob);
    simulateStop();
    
    // Should show processing state
    await waitFor(() => {
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
    });
    
    // Should eventually show completed state
    await waitFor(() => {
      expect(screen.getByText(/completed/i)).toBeInTheDocument();
    }, { timeout: 10000 });
    
    // Should show session in logs
    expect(screen.getByText(/session/i)).toBeInTheDocument();
  });

  it('should handle microphone permission denied', async () => {
    const user = userEvent.setup();
    
    // Mock getUserMedia to reject with permission error
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: vi.fn().mockRejectedValue(
          new DOMException('Permission denied', 'NotAllowedError')
        ),
      },
      writable: true,
    });
    
    renderWithProviders(<Index />);
    
    const recordButton = screen.getByRole('button', { name: /start recording|record/i });
    await user.click(recordButton);
    
    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/permission denied|microphone access/i)).toBeInTheDocument();
    });
  });

  it('should navigate between different testing modes', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Index />);
    
    // Should start on full pipeline
    expect(screen.getByText('Full Pipeline')).toBeInTheDocument();
    
    // Navigate to STT testing
    const sttTab = screen.getByText('STT Test');
    await user.click(sttTab);
    
    expect(screen.getByText('Speech-to-Text Testing')).toBeInTheDocument();
    
    // Navigate to LLM testing
    const llmTab = screen.getByText('LLM Test');
    await user.click(llmTab);
    
    expect(screen.getByText(/language model/i)).toBeInTheDocument();
    
    // Navigate to TTS testing
    const ttsTab = screen.getByText('TTS Test');
    await user.click(ttsTab);
    
    expect(screen.getByText(/text.*speech/i)).toBeInTheDocument();
    
    // Navigate to tools
    const toolsTab = screen.getByText('Tools');
    await user.click(toolsTab);
    
    expect(screen.getByText('Tools Management')).toBeInTheDocument();
    
    // Navigate to logs
    const logsTab = screen.getByText('Logs');
    await user.click(logsTab);
    
    expect(screen.getByText(/session.*logs|log viewer/i)).toBeInTheDocument();
  });

  it('should configure API keys through dialog', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Index />);
    
    // Find and click API configuration button
    const configButton = screen.getByRole('button', { name: /api.*config|settings/i });
    await user.click(configButton);
    
    // Should open configuration dialog
    await waitFor(() => {
      expect(screen.getByText('API Configuration')).toBeInTheDocument();
    });
    
    // Fill in API keys
    const deepgramInput = screen.getByLabelText(/deepgram/i);
    const openaiInput = screen.getByLabelText(/openai/i);
    const elevenlabsInput = screen.getByLabelText(/elevenlabs/i);
    
    await user.type(deepgramInput, '1234567890abcdef1234567890abcdef12345678');
    await user.type(openaiInput, 'sk-1234567890abcdef1234567890abcdef12345678');
    await user.type(elevenlabsInput, '1234567890abcdef1234567890abcdef12345678');
    
    // Save configuration
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/configuration.*saved/i)).toBeInTheDocument();
    });
  });

  it('should manage tools through tools tab', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Index />);
    
    // Navigate to tools tab
    const toolsTab = screen.getByText('Tools');
    await user.click(toolsTab);
    
    // Add a new tool
    const addToolButton = screen.getByText(/add.*tool/i);
    await user.click(addToolButton);
    
    // Fill tool form
    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    await user.type(screen.getByLabelText(/description/i), 'A test tool');
    await user.type(screen.getByLabelText(/webhook url/i), 'https://example.com/webhook');
    
    // Save tool
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Should show tool in list
    await waitFor(() => {
      expect(screen.getByText('Test Tool')).toBeInTheDocument();
    });
  });

  it('should handle STT testing independently', async () => {
    const user = userEvent.setup();
    const { mockRecorder, simulateDataAvailable, simulateStop } = mockMediaRecorder();
    
    global.MediaRecorder = vi.fn().mockImplementation(() => mockRecorder);
    
    renderWithProviders(<Index />);
    
    // Navigate to STT tab
    const sttTab = screen.getByText('STT Test');
    await user.click(sttTab);
    
    // Start recording in STT tester
    const recordButton = screen.getByRole('button', { name: /start recording|record/i });
    await user.click(recordButton);
    
    // Simulate recording
    const mockAudioBlob = createMockBlob();
    simulateDataAvailable(mockAudioBlob);
    simulateStop();
    
    // Should show transcription process
    await waitFor(() => {
      expect(screen.getByText(/transcrib/i)).toBeInTheDocument();
    });
  });

  it('should handle LLM testing with tools', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Index />);
    
    // First add a tool
    const toolsTab = screen.getByText('Tools');
    await user.click(toolsTab);
    
    const addToolButton = screen.getByText(/add.*tool/i);
    await user.click(addToolButton);
    
    await user.type(screen.getByLabelText(/tool name/i), 'Email Tool');
    await user.type(screen.getByLabelText(/webhook url/i), 'https://example.com/email');
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Navigate to LLM tab
    const llmTab = screen.getByText('LLM Test');
    await user.click(llmTab);
    
    // Enter input that should trigger tool usage
    const inputField = screen.getByRole('textbox', { name: /input|message/i });
    await user.type(inputField, 'Send an <NAME_EMAIL>');
    
    // Submit
    const submitButton = screen.getByRole('button', { name: /send|submit/i });
    await user.click(submitButton);
    
    // Should show processing and eventually response
    await waitFor(() => {
      expect(screen.getByText(/processing|response/i)).toBeInTheDocument();
    });
  });

  it('should handle TTS testing', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Index />);
    
    // Navigate to TTS tab
    const ttsTab = screen.getByText('TTS Test');
    await user.click(ttsTab);
    
    // Enter text to convert
    const textInput = screen.getByRole('textbox', { name: /text/i });
    await user.type(textInput, 'Hello, this is a test of text to speech.');
    
    // Generate audio
    const generateButton = screen.getByRole('button', { name: /generate/i });
    await user.click(generateButton);
    
    // Should show processing
    await waitFor(() => {
      expect(screen.getByText(/generat|process/i)).toBeInTheDocument();
    });
  });

  it('should persist session data in logs', async () => {
    const user = userEvent.setup();
    const { mockRecorder, simulateDataAvailable, simulateStop } = mockMediaRecorder();
    
    global.MediaRecorder = vi.fn().mockImplementation(() => mockRecorder);
    
    renderWithProviders(<Index />);
    
    // Complete a recording session
    const recordButton = screen.getByRole('button', { name: /start recording|record/i });
    await user.click(recordButton);
    
    const mockAudioBlob = createMockBlob();
    simulateDataAvailable(mockAudioBlob);
    simulateStop();
    
    // Wait for completion
    await waitFor(() => {
      expect(screen.getByText(/completed/i)).toBeInTheDocument();
    }, { timeout: 10000 });
    
    // Navigate to logs
    const logsTab = screen.getByText('Logs');
    await user.click(logsTab);
    
    // Should show the session in logs
    await waitFor(() => {
      expect(screen.getByText(/session/i)).toBeInTheDocument();
    });
  });

  it('should handle errors gracefully throughout the pipeline', async () => {
    const user = userEvent.setup();
    
    // Mock console.error to avoid noise in tests
    const originalError = console.error;
    console.error = vi.fn();
    
    renderWithProviders(<Index />);
    
    // Try to record with failing getUserMedia
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: vi.fn().mockRejectedValue(new Error('Device error')),
      },
      writable: true,
    });
    
    const recordButton = screen.getByRole('button', { name: /start recording|record/i });
    await user.click(recordButton);
    
    // Should handle error without crashing
    await waitFor(() => {
      expect(screen.getByText(/error|failed/i)).toBeInTheDocument();
    });
    
    // Restore console.error
    console.error = originalError;
  });
});
