import { z } from 'zod';

/**
 * Validation schemas and service for input sanitization and validation
 */

// API Key validation schemas
export const apiKeySchema = z.string()
  .min(10, 'API key must be at least 10 characters')
  .max(200, 'API key must be less than 200 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'API key contains invalid characters')
  .refine(key => !key.includes(' '), 'API key cannot contain spaces');

export const deepgramKeySchema = apiKeySchema
  .refine(key => key.length >= 32, 'Deepgram API key should be at least 32 characters');

export const openaiKeySchema = z.string()
  .regex(/^sk-[a-zA-Z0-9]{48}$/, 'Invalid OpenAI API key format');

export const elevenlabsKeySchema = apiKeySchema
  .refine(key => key.length >= 32, 'ElevenLabs API key should be at least 32 characters');

// System prompt validation
export const systemPromptSchema = z.string()
  .min(1, 'System prompt cannot be empty')
  .max(2000, 'System prompt must be less than 2000 characters')
  .refine(prompt => prompt.trim().length > 0, 'System prompt cannot be only whitespace');

// User input validation
export const userInputSchema = z.string()
  .max(1000, 'Input must be less than 1000 characters')
  .refine(input => input.trim().length > 0, 'Input cannot be only whitespace');

// Tool configuration validation
export const toolParameterSchema = z.record(z.string(), z.enum(['string', 'number', 'boolean', 'array', 'object']));

export const toolSchema = z.object({
  id: z.string().min(1, 'Tool ID is required'),
  name: z.string()
    .min(1, 'Tool name is required')
    .max(100, 'Tool name must be less than 100 characters')
    .regex(/^[a-zA-Z0-9_\s-]+$/, 'Tool name contains invalid characters'),
  description: z.string()
    .max(500, 'Tool description must be less than 500 characters'),
  webhookUrl: z.string()
    .url('Invalid webhook URL')
    .refine(url => url.startsWith('https://'), 'Webhook URL must use HTTPS'),
  parameters: toolParameterSchema,
  enabled: z.boolean()
});

// Voice selection validation
export const voiceIdSchema = z.string()
  .min(1, 'Voice ID is required')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Invalid voice ID format');

// Session data validation
export const sessionLogSchema = z.object({
  id: z.string().min(1, 'Session ID is required'),
  timestamp: z.string().datetime('Invalid timestamp format'),
  inputAudio: z.instanceof(Blob).nullable(),
  transcript: z.string().max(5000, 'Transcript too long'),
  gptResponse: z.string().max(5000, 'Response too long'),
  ttsAudio: z.instanceof(Blob).nullable(),
  status: z.enum(['pending', 'processing', 'completed', 'error'])
});

// File upload validation
export const audioFileSchema = z.instanceof(File)
  .refine(file => file.size <= 10 * 1024 * 1024, 'Audio file must be less than 10MB')
  .refine(file => ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/webm'].includes(file.type), 
    'Invalid audio file type. Supported: WAV, MP3, WebM');

export class ValidationService {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  static sanitizeHtml(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize and validate API key
   */
  static validateApiKey(key: string, service: 'deepgram' | 'openai' | 'elevenlabs'): { isValid: boolean; error?: string; sanitized?: string } {
    try {
      const sanitized = key.trim();
      
      let schema;
      switch (service) {
        case 'deepgram':
          schema = deepgramKeySchema;
          break;
        case 'openai':
          schema = openaiKeySchema;
          break;
        case 'elevenlabs':
          schema = elevenlabsKeySchema;
          break;
        default:
          return { isValid: false, error: 'Unknown service' };
      }

      schema.parse(sanitized);
      return { isValid: true, sanitized };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.errors[0].message };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validate and sanitize system prompt
   */
  static validateSystemPrompt(prompt: string): { isValid: boolean; error?: string; sanitized?: string } {
    try {
      const sanitized = this.sanitizeHtml(prompt.trim());
      systemPromptSchema.parse(sanitized);
      return { isValid: true, sanitized };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.errors[0].message };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validate user input
   */
  static validateUserInput(input: string): { isValid: boolean; error?: string; sanitized?: string } {
    try {
      const sanitized = this.sanitizeHtml(input.trim());
      userInputSchema.parse(sanitized);
      return { isValid: true, sanitized };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.errors[0].message };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validate tool configuration
   */
  static validateTool(tool: unknown): { isValid: boolean; error?: string; validated?: any } {
    try {
      const validated = toolSchema.parse(tool);
      return { isValid: true, validated };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.errors[0].message };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validate audio file
   */
  static validateAudioFile(file: File): { isValid: boolean; error?: string } {
    try {
      audioFileSchema.parse(file);
      return { isValid: true };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.errors[0].message };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validate webhook URL
   */
  static validateWebhookUrl(url: string): { isValid: boolean; error?: string; sanitized?: string } {
    try {
      const sanitized = url.trim();
      z.string().url().refine(url => url.startsWith('https://')).parse(sanitized);
      return { isValid: true, sanitized };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: 'Invalid HTTPS URL' };
      }
      return { isValid: false, error: 'Validation failed' };
    }
  }
}

export default ValidationService;
