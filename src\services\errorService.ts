import type { AppError } from '@/types';

export enum ErrorCode {
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // Authentication errors
  AUTH_ERROR = 'AUTH_ERROR',
  INVALID_API_KEY = 'INVALID_API_KEY',
  
  // Audio errors
  MICROPHONE_ACCESS_DENIED = 'MICROPHONE_ACCESS_DENIED',
  AUDIO_RECORDING_FAILED = 'AUDIO_RECORDING_FAILED',
  AUDIO_PLAYBACK_FAILED = 'AUDIO_PLAYBACK_FAILED',
  
  // Processing errors
  STT_PROCESSING_ERROR = 'STT_PROCESSING_ERROR',
  LLM_PROCESSING_ERROR = 'LLM_PROCESSING_ERROR',
  TTS_PROCESSING_ERROR = 'TTS_PROCESSING_ERROR',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  
  // Configuration errors
  CONFIG_ERROR = 'CONFIG_ERROR',
  MISSING_CONFIG = 'MISSING_CONFIG',
  
  // Tool errors
  TOOL_EXECUTION_ERROR = 'TOOL_EXECUTION_ERROR',
  TOOL_VALIDATION_ERROR = 'TOOL_VALIDATION_ERROR',
  
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  COMPONENT_ERROR = 'COMPONENT_ERROR'
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  additionalData?: Record<string, unknown>;
}

class ErrorService {
  private static instance: ErrorService;
  private errorHandlers: Map<ErrorCode, (error: AppError) => void> = new Map();
  private errorQueue: AppError[] = [];
  private isOnline: boolean = navigator.onLine;

  private constructor() {
    this.setupGlobalErrorHandlers();
    this.setupNetworkMonitoring();
  }

  public static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = this.createError(
        ErrorCode.UNKNOWN_ERROR,
        'Unhandled promise rejection',
        { originalError: event.reason }
      );
      this.handleError(error);
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      const error = this.createError(
        ErrorCode.COMPONENT_ERROR,
        event.message,
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        }
      );
      this.handleError(error);
    });
  }

  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  public createError(
    code: ErrorCode,
    message: string,
    details?: Record<string, unknown>,
    context?: ErrorContext
  ): AppError {
    return {
      code,
      message,
      details: {
        ...details,
        context,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
      stack: new Error().stack
    };
  }

  public handleError(error: AppError): void {
    console.error('Error handled by ErrorService:', error);

    // Execute registered error handlers
    const handler = this.errorHandlers.get(error.code as ErrorCode);
    if (handler) {
      try {
        handler(error);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
      }
    }

    // Queue error for reporting if offline
    if (!this.isOnline) {
      this.errorQueue.push(error);
      return;
    }

    // Report error immediately
    this.reportError(error);
  }

  public registerErrorHandler(code: ErrorCode, handler: (error: AppError) => void): void {
    this.errorHandlers.set(code, handler);
  }

  public unregisterErrorHandler(code: ErrorCode): void {
    this.errorHandlers.delete(code);
  }

  private async reportError(error: AppError): Promise<void> {
    try {
      // In a real application, this would send to an error tracking service
      // like Sentry, LogRocket, Bugsnag, etc.
      
      const errorReport = {
        ...error,
        environment: process.env.NODE_ENV,
        version: process.env.REACT_APP_VERSION || 'unknown',
        buildId: process.env.REACT_APP_BUILD_ID || 'unknown'
      };

      // Example: Send to error tracking service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });

      console.log('Error reported:', errorReport);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
      // Store in local storage as fallback
      this.storeErrorLocally(error);
    }
  }

  private storeErrorLocally(error: AppError): void {
    try {
      const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      storedErrors.push(error);
      
      // Keep only last 50 errors
      if (storedErrors.length > 50) {
        storedErrors.splice(0, storedErrors.length - 50);
      }
      
      localStorage.setItem('app_errors', JSON.stringify(storedErrors));
    } catch (storageError) {
      console.error('Failed to store error locally:', storageError);
    }
  }

  private processErrorQueue(): void {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    errors.forEach(error => this.reportError(error));
  }

  public getStoredErrors(): AppError[] {
    try {
      return JSON.parse(localStorage.getItem('app_errors') || '[]');
    } catch {
      return [];
    }
  }

  public clearStoredErrors(): void {
    localStorage.removeItem('app_errors');
  }

  // Utility methods for common error scenarios
  public handleNetworkError(originalError: unknown, context?: ErrorContext): AppError {
    const error = this.createError(
      ErrorCode.NETWORK_ERROR,
      'Network request failed',
      { originalError },
      context
    );
    this.handleError(error);
    return error;
  }

  public handleValidationError(message: string, field?: string, context?: ErrorContext): AppError {
    const error = this.createError(
      ErrorCode.VALIDATION_ERROR,
      message,
      { field },
      context
    );
    this.handleError(error);
    return error;
  }

  public handleAPIError(status: number, message: string, context?: ErrorContext): AppError {
    const error = this.createError(
      ErrorCode.API_ERROR,
      `API Error: ${message}`,
      { status },
      context
    );
    this.handleError(error);
    return error;
  }

  public handleAudioError(message: string, context?: ErrorContext): AppError {
    const error = this.createError(
      ErrorCode.AUDIO_RECORDING_FAILED,
      message,
      {},
      context
    );
    this.handleError(error);
    return error;
  }

  // Error recovery utilities
  public async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context?: ErrorContext
  ): Promise<T> {
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          const appError = this.createError(
            ErrorCode.UNKNOWN_ERROR,
            `Operation failed after ${maxRetries} attempts`,
            { originalError: error, attempts: attempt },
            context
          );
          this.handleError(appError);
          throw appError;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw lastError;
  }
}

export default ErrorService;
