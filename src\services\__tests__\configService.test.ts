import { describe, it, expect, beforeEach, vi } from 'vitest';
import ConfigService from '../configService';

describe('ConfigService', () => {
  let configService: ConfigService;

  beforeEach(() => {
    // Clear any existing instance
    (ConfigService as any).instance = undefined;
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear environment variables
    vi.clearAllMocks();
    
    configService = ConfigService.getInstance();
  });

  describe('getInstance', () => {
    it('should return a singleton instance', () => {
      const instance1 = ConfigService.getInstance();
      const instance2 = ConfigService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('updateApiKey', () => {
    it('should update and store a valid API key', () => {
      const validKey = 'sk-1234567890abcdef1234567890abcdef12345678';
      
      expect(() => {
        configService.updateApiKey('openaiApiKey', validKey);
      }).not.toThrow();
      
      const config = configService.getConfig();
      expect(config.openaiApiKey).toBe(validKey);
    });

    it('should throw error for invalid API key', () => {
      const invalidKey = 'invalid';
      
      expect(() => {
        configService.updateApiKey('openaiApiKey', invalidKey);
      }).toThrow('Invalid API key format');
    });

    it('should store API key in session storage', () => {
      const validKey = 'sk-1234567890abcdef1234567890abcdef12345678';
      const setItemSpy = vi.spyOn(sessionStorage, 'setItem');
      
      configService.updateApiKey('openaiApiKey', validKey);
      
      expect(setItemSpy).toHaveBeenCalledWith(
        'vapi_openai_key',
        expect.any(String)
      );
    });
  });

  describe('isConfigured', () => {
    it('should return false when no keys are configured', () => {
      expect(configService.isConfigured()).toBe(false);
    });

    it('should return true when all keys are configured', () => {
      configService.updateApiKey('deepgramApiKey', '1234567890abcdef1234567890abcdef');
      configService.updateApiKey('openaiApiKey', 'sk-1234567890abcdef1234567890abcdef12345678');
      configService.updateApiKey('elevenlabsApiKey', '1234567890abcdef1234567890abcdef');
      
      expect(configService.isConfigured()).toBe(true);
    });
  });

  describe('getApiKey', () => {
    it('should return the API key when configured', () => {
      const validKey = 'sk-1234567890abcdef1234567890abcdef12345678';
      configService.updateApiKey('openaiApiKey', validKey);
      
      expect(configService.getApiKey('openaiApiKey')).toBe(validKey);
    });

    it('should throw error when API key is not configured', () => {
      expect(() => {
        configService.getApiKey('openaiApiKey');
      }).toThrow('openaiApiKey not configured');
    });
  });

  describe('clearConfiguration', () => {
    it('should clear all API keys but preserve other config', () => {
      configService.updateApiKey('openaiApiKey', 'sk-1234567890abcdef1234567890abcdef12345678');
      
      const configBefore = configService.getConfig();
      expect(configBefore.openaiApiKey).toBeTruthy();
      
      configService.clearConfiguration();
      
      const configAfter = configService.getConfig();
      expect(configAfter.openaiApiKey).toBeFalsy();
      expect(configAfter.apiBaseUrl).toBeTruthy(); // Should preserve non-API key config
    });

    it('should clear session storage', () => {
      const clearSpy = vi.spyOn(sessionStorage, 'clear');
      
      configService.clearConfiguration();
      
      expect(clearSpy).toHaveBeenCalled();
    });
  });

  describe('validateConfiguration', () => {
    it('should return errors when no keys are configured', () => {
      const result = configService.validateConfiguration();
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
      expect(result.errors).toContain('Deepgram API key is required');
      expect(result.errors).toContain('OpenAI API key is required');
      expect(result.errors).toContain('ElevenLabs API key is required');
    });

    it('should return no errors when all keys are configured', () => {
      configService.updateApiKey('deepgramApiKey', '1234567890abcdef1234567890abcdef');
      configService.updateApiKey('openaiApiKey', 'sk-1234567890abcdef1234567890abcdef12345678');
      configService.updateApiKey('elevenlabsApiKey', '1234567890abcdef1234567890abcdef');
      
      const result = configService.validateConfiguration();
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('storage error handling', () => {
    it('should handle storage errors gracefully', () => {
      // Mock sessionStorage to throw an error
      const originalSetItem = sessionStorage.setItem;
      sessionStorage.setItem = vi.fn().mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });
      
      // Should not throw even if storage fails
      expect(() => {
        configService.updateApiKey('openaiApiKey', 'sk-1234567890abcdef1234567890abcdef12345678');
      }).not.toThrow();
      
      // Restore original implementation
      sessionStorage.setItem = originalSetItem;
    });
  });
});
