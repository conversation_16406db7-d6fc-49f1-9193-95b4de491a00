import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Settings, Webhook, TestTube } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export interface Tool {
  id: string;
  name: string;
  description: string;
  webhookUrl: string;
  parameters: Record<string, string>;
  enabled: boolean;
}

interface ToolsManagerProps {
  tools: Tool[];
  onToolsChange: (tools: Tool[]) => void;
}

const ToolsManager: React.FC<ToolsManagerProps> = ({ tools, onToolsChange }) => {
  const [isAddingTool, setIsAddingTool] = useState(false);
  const [editingTool, setEditingTool] = useState<string | null>(null);
  const [newTool, setNewTool] = useState<Partial<Tool>>({
    name: '',
    description: '',
    webhookUrl: '',
    parameters: {},
    enabled: true
  });
  const [paramKey, setParamKey] = useState('');
  const [paramType, setParamType] = useState('string');
  const { toast } = useToast();

  const parameterTypes = [
    { value: 'string', label: 'String' },
    { value: 'number', label: 'Number' },
    { value: 'boolean', label: 'Boolean' },
    { value: 'email', label: 'Email' },
    { value: 'url', label: 'URL' },
    { value: 'date', label: 'Date' },
    { value: 'array', label: 'Array' },
    { value: 'object', label: 'Object' }
  ];

  const addParameter = () => {
    if (paramKey && newTool.parameters) {
      setNewTool({
        ...newTool,
        parameters: {
          ...newTool.parameters,
          [paramKey]: paramType
        }
      });
      setParamKey('');
      setParamType('string');
    }
  };

  const removeParameter = (key: string) => {
    if (newTool.parameters) {
      const updatedParams = { ...newTool.parameters };
      delete updatedParams[key];
      setNewTool({ ...newTool, parameters: updatedParams });
    }
  };

  const saveTool = () => {
    if (newTool.name && newTool.webhookUrl) {
      const tool: Tool = {
        id: editingTool || `tool_${Date.now()}`,
        name: newTool.name,
        description: newTool.description || '',
        webhookUrl: newTool.webhookUrl,
        parameters: newTool.parameters || {},
        enabled: newTool.enabled !== false
      };

      if (editingTool) {
        onToolsChange(tools.map(t => t.id === editingTool ? tool : t));
      } else {
        onToolsChange([...tools, tool]);
      }

      resetForm();
      toast({
        title: "Tool Saved",
        description: `Tool "${tool.name}" has been ${editingTool ? 'updated' : 'added'} successfully.`
      });
    }
  };

  const resetForm = () => {
    setNewTool({
      name: '',
      description: '',
      webhookUrl: '',
      parameters: {},
      enabled: true
    });
    setIsAddingTool(false);
    setEditingTool(null);
  };

  const deleteTool = (toolId: string) => {
    onToolsChange(tools.filter(t => t.id !== toolId));
    toast({
      title: "Tool Deleted",
      description: "Tool has been removed successfully."
    });
  };

  const toggleTool = (toolId: string) => {
    onToolsChange(tools.map(t => 
      t.id === toolId ? { ...t, enabled: !t.enabled } : t
    ));
  };

  const editTool = (tool: Tool) => {
    setNewTool(tool);
    setEditingTool(tool.id);
    setIsAddingTool(true);
  };

  const testTool = async (tool: Tool) => {
    try {
      const testData: Record<string, any> = {};
      Object.keys(tool.parameters).forEach(key => {
        testData[key] = `test_${key}`;
      });

      const response = await fetch(tool.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      toast({
        title: "Tool Test",
        description: `Test request sent to ${tool.name}. Status: ${response.status}`,
        variant: response.ok ? "default" : "destructive"
      });
    } catch (error) {
      toast({
        title: "Tool Test Failed",
        description: `Failed to test ${tool.name}: ${error}`,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
        <CardHeader>
          <CardTitle className="text-2xl text-orange-900 flex items-center gap-2">
            <Webhook className="w-6 h-6" />
            AI Tools Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Existing Tools */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Configured Tools</h3>
              <Button
                onClick={() => setIsAddingTool(true)}
                className="bg-orange-500 hover:bg-orange-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Tool
              </Button>
            </div>

            {tools.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No tools configured yet. Add your first tool to get started.
              </div>
            ) : (
              <div className="grid gap-4">
                {tools.map((tool) => (
                  <Card key={tool.id} className="bg-white border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold">{tool.name}</h4>
                            <Badge 
                              variant={tool.enabled ? "default" : "secondary"}
                              className={tool.enabled ? "bg-green-100 text-green-800" : ""}
                            >
                              {tool.enabled ? "Active" : "Disabled"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{tool.description}</p>
                          <p className="text-xs text-gray-500 font-mono">{tool.webhookUrl}</p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {Object.keys(tool.parameters).map(param => (
                              <Badge key={param} variant="outline" className="text-xs">
                                {param}: {tool.parameters[param]}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => testTool(tool)}
                          >
                            <TestTube className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleTool(tool.id)}
                          >
                            <Settings className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editTool(tool)}
                          >
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => deleteTool(tool.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Add/Edit Tool Form */}
          {isAddingTool && (
            <Card className="bg-white border-orange-200">
              <CardHeader>
                <CardTitle className="text-lg">
                  {editingTool ? 'Edit Tool' : 'Add New Tool'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Tool Name</Label>
                    <Input
                      value={newTool.name || ''}
                      onChange={(e) => setNewTool({ ...newTool, name: e.target.value })}
                      placeholder="e.g., Send Email"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Webhook URL</Label>
                    <Input
                      value={newTool.webhookUrl || ''}
                      onChange={(e) => setNewTool({ ...newTool, webhookUrl: e.target.value })}
                      placeholder="https://mydomain.com/webhook"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea
                    value={newTool.description || ''}
                    onChange={(e) => setNewTool({ ...newTool, description: e.target.value })}
                    placeholder="What does this tool do? When should the AI use it?"
                    className="min-h-[60px]"
                  />
                </div>

                <div className="space-y-3">
                  <Label>Parameters</Label>
                  <div className="flex gap-2">
                    <Input
                      value={paramKey}
                      onChange={(e) => setParamKey(e.target.value)}
                      placeholder="Parameter name"
                      className="flex-1"
                    />
                    <Select value={paramType} onValueChange={setParamType}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {parameterTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button onClick={addParameter} size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {newTool.parameters && Object.keys(newTool.parameters).length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(newTool.parameters).map(([key, type]) => (
                        <Badge key={key} variant="outline" className="flex items-center gap-1">
                          {key}: {type}
                          <button onClick={() => removeParameter(key)}>
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={resetForm}>
                    Cancel
                  </Button>
                  <Button onClick={saveTool} className="bg-orange-500 hover:bg-orange-600">
                    {editingTool ? 'Update Tool' : 'Save Tool'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ToolsManager;
