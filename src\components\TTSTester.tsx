
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Play, Download, Volume2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ModelSelector from './ModelSelector';

const TTSTester = () => {
  const [text, setText] = useState('');
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [provider, setProvider] = useState('elevenlabs');
  const [voice, setVoice] = useState('Aria - 9BWtsMINqrJLrRacOk9x');
  const [customVoiceId, setCustomVoiceId] = useState('');
  const [useCustomVoice, setUseCustomVoice] = useState(false);
  const [model, setModel] = useState('eleven_multilingual_v2');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();



  const handleGenerate = async () => {
    if (!text.trim()) return;
    
    setIsProcessing(true);
    // Simulate TTS processing
    setTimeout(() => {
      // Create a fake audio blob for demo
      const fakeAudio = new Blob(['fake audio data'], { type: 'audio/mp3' });
      setAudioBlob(fakeAudio);
      setIsProcessing(false);
      toast({
        title: "Audio Generated",
        description: `Generated with ${provider} (${model})`
      });
    }, 2000);
  };

  const handlePlayAudio = () => {
    if (audioBlob) {
      // In a real implementation, this would play the actual audio
      toast({
        title: "Playing Audio",
        description: "Audio playback would start here"
      });
    }
  };

  const handleDownloadAudio = () => {
    if (audioBlob) {
      const url = URL.createObjectURL(audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tts-output-${Date.now()}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const quickTexts = [
    "Hello, this is a test of the text-to-speech system.",
    "The quick brown fox jumps over the lazy dog.",
    "Welcome to our voice AI testing framework. How may I assist you today?",
    "Thank you for using our service. Have a wonderful day!"
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-2xl text-green-900 flex items-center gap-2">
            <Volume2 className="w-6 h-6" />
            Text-to-Speech Testing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Model and Voice Selection */}
          <ModelSelector
            category="tts"
            selectedProvider={provider}
            selectedModel={model}
            selectedVoice={voice}
            onProviderChange={setProvider}
            onModelChange={setModel}
            onVoiceChange={setVoice}
          />

          {/* Custom Voice ID Option */}
          <div className="space-y-2">
            <Label>Custom Voice ID (Optional)</Label>
            <Input
              value={customVoiceId}
              onChange={(e) => setCustomVoiceId(e.target.value)}
              placeholder="Enter custom voice ID to override selection above"
            />
          </div>

          {/* Text Input */}
          <div className="space-y-2">
            <Label>Text to Convert</Label>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter the text you want to convert to speech..."
              className="min-h-[120px] bg-white"
            />
            <div className="flex flex-wrap gap-2">
              {quickTexts.map((quickText, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setText(quickText)}
                  className="text-xs"
                >
                  Sample {index + 1}
                </Button>
              ))}
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleGenerate}
              disabled={isProcessing || !text.trim()}
              size="lg"
              className="bg-green-500 hover:bg-green-600"
            >
              <Volume2 className="w-4 h-4 mr-2" />
              {isProcessing ? 'Generating...' : 'Generate Speech'}
            </Button>
          </div>

          {/* Audio Controls */}
          {audioBlob && (
            <div className="flex justify-center space-x-4 pt-4 border-t">
              <Button variant="outline" onClick={handlePlayAudio}>
                <Play className="w-4 h-4 mr-2" />
                Play Audio
              </Button>
              <Button variant="outline" onClick={handleDownloadAudio}>
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TTSTester;
