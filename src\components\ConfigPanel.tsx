
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Settings, Sparkles } from 'lucide-react';

interface ConfigPanelProps {
  systemPrompt: string;
  onSystemPromptChange: (prompt: string) => void;
  selectedVoice: string;
  onVoiceChange: (voice: string) => void;
}

const ConfigPanel: React.FC<ConfigPanelProps> = ({
  systemPrompt,
  onSystemPromptChange,
  selectedVoice,
  onVoiceChange
}) => {
  const voices = [
    { id: '9BWtsMINqrJLrRacOk9x', name: '<PERSON>' },
    { id: 'CwhRBWXzGAHq8TQ4Fs17', name: '<PERSON>' },
    { id: 'EXAVITQu4vr4xnSDxMaL', name: '<PERSON>' },
    { id: 'FGY2WhTYpPnrIDTdsKH5', name: 'Laura' },
    { id: 'IKne3meq5aSn9XLyUdCD', name: 'Charlie' },
    { id: 'JBFqnCBsd6RMkjVDRZzb', name: 'George' },
    { id: 'N2lVS1w4EtoT3dr4eOWO', name: 'Callum' },
    { id: 'SAz9YHcvj6GT2YYXdXww', name: 'River' }
  ];

  return (
    <Card className="bg-white/70 backdrop-blur-sm border-white/20 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg flex items-center gap-2 text-gray-800">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
            <Settings className="w-4 h-4 text-white" />
          </div>
          Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Prompt */}
        <div className="space-y-3">
          <Label htmlFor="system-prompt" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            System Prompt
          </Label>
          <Textarea
            id="system-prompt"
            value={systemPrompt}
            onChange={(e) => onSystemPromptChange(e.target.value)}
            placeholder="Enter your system prompt for the AI assistant..."
            className="bg-white/80 border-gray-200 min-h-[100px] resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="text-xs text-gray-500">
            {systemPrompt.length} characters
          </div>
        </div>

        {/* Voice Selection */}
        <div className="space-y-3">
          <Label htmlFor="voice-select" className="text-sm font-semibold text-gray-700">
            ElevenLabs Voice
          </Label>
          <Select value={selectedVoice} onValueChange={onVoiceChange}>
            <SelectTrigger className="bg-white/80 border-gray-200">
              <SelectValue placeholder="Select a voice" />
            </SelectTrigger>
            <SelectContent className="bg-white border-gray-200">
              {voices.map((voice) => (
                <SelectItem key={voice.id} value={`${voice.name} - ${voice.id}`}>
                  {voice.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Provider Settings */}
        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="text-sm font-semibold text-gray-700">Provider Status</div>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
              <span className="text-gray-700 font-medium">Deepgram STT</span>
              <span className="text-green-600 font-semibold flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Connected
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <span className="text-gray-700 font-medium">OpenAI GPT-4</span>
              <span className="text-blue-600 font-semibold flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Connected
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
              <span className="text-gray-700 font-medium">ElevenLabs TTS</span>
              <span className="text-purple-600 font-semibold flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                Connected
              </span>
            </div>
          </div>
        </div>

        {/* Quick Presets */}
        <div className="space-y-3 pt-4 border-t border-gray-200">
          <Label className="text-sm font-semibold text-gray-700">Quick Presets</Label>
          <div className="grid grid-cols-1 gap-2">
            <Button
              onClick={() => onSystemPromptChange("You are a helpful assistant. Be concise and friendly.")}
              variant="outline"
              size="sm"
              className="bg-white/80 hover:bg-blue-50 border-gray-200 text-left justify-start"
            >
              <span className="text-xs">🤖 Helpful Assistant</span>
            </Button>
            <Button
              onClick={() => onSystemPromptChange("You are a customer service representative. Be polite and solution-focused.")}
              variant="outline"
              size="sm"
              className="bg-white/80 hover:bg-green-50 border-gray-200 text-left justify-start"
            >
              <span className="text-xs">🎧 Customer Support</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConfigPanel;
