import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAppContext } from '@/context/AppContext';
import type { SessionLog, ProcessingMetrics } from '@/types';

interface UseSessionManagerReturn {
  currentSession: string | null;
  sessionLogs: SessionLog[];
  isRecording: boolean;
  processingStage: string;
  startSession: () => string;
  endSession: (sessionData: Partial<SessionLog>) => void;
  updateSessionStage: (stage: string) => void;
  simulateProcessing: (audioBlob: Blob) => Promise<void>;
}

export const useSessionManager = (): UseSessionManagerReturn => {
  const { state, actions } = useAppContext();
  const { toast } = useToast();

  const startSession = useCallback((): string => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    actions.startSession(sessionId);
    
    toast({
      title: "Session Started",
      description: `New session: ${sessionId.split('_')[1]}`
    });
    
    return sessionId;
  }, [actions, toast]);

  const endSession = useCallback((sessionData: Partial<SessionLog>) => {
    if (!state.session.current) return;

    const sessionLog: SessionLog = {
      id: state.session.current,
      timestamp: new Date().toISOString(),
      inputAudio: null,
      transcript: '',
      gptResponse: '',
      ttsAudio: null,
      status: 'completed',
      ...sessionData
    };

    actions.endSession(sessionLog);
    
    toast({
      title: "Session Complete",
      description: "Session data has been saved"
    });
  }, [state.session.current, actions, toast]);

  const updateSessionStage = useCallback((stage: string) => {
    actions.setProcessingStage(stage as any);
  }, [actions]);

  const simulateProcessing = useCallback(async (audioBlob: Blob): Promise<void> => {
    if (!state.session.current) return;

    try {
      actions.setProcessingStage('processing');

      // Simulate STT processing
      await new Promise(resolve => setTimeout(resolve, 1500));
      actions.setProcessingStage('processing');

      // Simulate LLM processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      actions.setProcessingStage('processing');

      // Simulate TTS processing
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create mock session data
      const mockMetrics: ProcessingMetrics = {
        sttLatency: 1.5,
        llmProcessing: 2.0,
        ttsGeneration: 1.5,
        totalTime: 5.0
      };

      const sessionData: Partial<SessionLog> = {
        inputAudio: audioBlob,
        transcript: "This is a sample transcript from the STT service. The audio has been processed successfully.",
        gptResponse: "Thank you for testing the voice AI pipeline. This is a simulated response from the language model that would normally process your speech and generate an appropriate reply.",
        ttsAudio: null, // In a real implementation, this would be the generated audio
        status: 'completed',
        metrics: mockMetrics
      };

      endSession(sessionData);
      actions.setProcessingStage('completed');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Processing failed';
      
      actions.addError({
        code: 'PROCESSING_ERROR',
        message: errorMessage,
        timestamp: new Date().toISOString()
      });

      actions.setProcessingStage('error');
      
      toast({
        title: "Processing Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [state.session.current, actions, endSession, toast]);

  return {
    currentSession: state.session.current,
    sessionLogs: state.session.logs,
    isRecording: state.session.isRecording,
    processingStage: state.session.processingStage,
    startSession,
    endSession,
    updateSessionStage,
    simulateProcessing
  };
};
