import { describe, it, expect } from 'vitest';
import { screen } from '@testing-library/react';
import { renderWithProviders } from '@/test/utils';
import ProcessingPipeline from '../ProcessingPipeline';
import type { ProcessingPipelineProps } from '@/types';

describe('ProcessingPipeline', () => {
  const defaultProps: ProcessingPipelineProps = {
    stage: 'idle',
    currentSession: null,
  };

  it('should render processing pipeline component', () => {
    renderWithProviders(<ProcessingPipeline {...defaultProps} />);
    
    expect(screen.getByText('Processing Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Speech-to-Text')).toBeInTheDocument();
    expect(screen.getByText('Language Model')).toBeInTheDocument();
    expect(screen.getByText('Text-to-Speech')).toBeInTheDocument();
  });

  it('should show all stages as pending when idle', () => {
    renderWithProviders(<ProcessingPipeline {...defaultProps} stage="idle" />);
    
    // All stages should be in pending state
    const stageElements = screen.getAllByText(/pending|waiting/i);
    expect(stageElements.length).toBeGreaterThan(0);
  });

  it('should show STT as active when recording', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="recording" />
    );
    
    // STT stage should be active/highlighted
    const sttSection = screen.getByText('Speech-to-Text').closest('[data-testid="pipeline-stage"]');
    expect(sttSection).toHaveClass('active'); // Adjust class name based on implementation
  });

  it('should show LLM as active when processing', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="processing" />
    );
    
    // LLM stage should be active
    const llmSection = screen.getByText('Language Model').closest('[data-testid="pipeline-stage"]');
    expect(llmSection).toHaveClass('active'); // Adjust class name based on implementation
  });

  it('should show all stages as completed when finished', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="completed" />
    );
    
    // All stages should show as completed
    const completedIndicators = screen.getAllByTestId('stage-completed');
    expect(completedIndicators).toHaveLength(3);
  });

  it('should display session information when provided', () => {
    renderWithProviders(
      <ProcessingPipeline 
        {...defaultProps} 
        currentSession="session_123"
        stage="processing"
      />
    );
    
    expect(screen.getByText(/session_123/i)).toBeInTheDocument();
  });

  it('should show performance metrics when completed', () => {
    const metricsProps = {
      ...defaultProps,
      stage: 'completed' as const,
      metrics: {
        sttLatency: 1.2,
        llmProcessing: 2.8,
        ttsGeneration: 1.5,
        totalTime: 5.5,
      },
    };

    renderWithProviders(<ProcessingPipeline {...metricsProps} />);
    
    expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
    expect(screen.getByText('1.2s')).toBeInTheDocument(); // STT latency
    expect(screen.getByText('2.8s')).toBeInTheDocument(); // LLM processing
    expect(screen.getByText('1.5s')).toBeInTheDocument(); // TTS generation
  });

  it('should show error state when stage is error', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="error" />
    );
    
    // Should show error indicators
    const errorIndicators = screen.getAllByTestId('stage-error');
    expect(errorIndicators.length).toBeGreaterThan(0);
  });

  it('should display correct stage descriptions', () => {
    renderWithProviders(<ProcessingPipeline {...defaultProps} />);
    
    expect(screen.getByText('Deepgram transcription')).toBeInTheDocument();
    expect(screen.getByText('OpenAI GPT-4 processing')).toBeInTheDocument();
    expect(screen.getByText('ElevenLabs synthesis')).toBeInTheDocument();
  });

  it('should show stage numbers correctly', () => {
    renderWithProviders(<ProcessingPipeline {...defaultProps} />);
    
    expect(screen.getByText('1')).toBeInTheDocument(); // STT stage
    expect(screen.getByText('2')).toBeInTheDocument(); // LLM stage
    expect(screen.getByText('3')).toBeInTheDocument(); // TTS stage
  });

  it('should handle stage transitions correctly', () => {
    const { rerender } = renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="idle" />
    );
    
    // Start recording
    rerender(<ProcessingPipeline {...defaultProps} stage="recording" />);
    
    // Move to processing
    rerender(<ProcessingPipeline {...defaultProps} stage="processing" />);
    
    // Complete
    rerender(<ProcessingPipeline {...defaultProps} stage="completed" />);
    
    // Should show completed state
    const completedIndicators = screen.getAllByTestId('stage-completed');
    expect(completedIndicators).toHaveLength(3);
  });

  it('should show loading indicators for active stages', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="processing" />
    );
    
    // Should show loading/spinner for active stage
    const loadingIndicator = screen.getByTestId('stage-loading');
    expect(loadingIndicator).toBeInTheDocument();
  });

  it('should display stage icons correctly', () => {
    renderWithProviders(<ProcessingPipeline {...defaultProps} />);
    
    // Check for stage icons (adjust based on actual implementation)
    const micIcon = screen.getByTestId('mic-icon');
    const messageIcon = screen.getByTestId('message-icon');
    const volumeIcon = screen.getByTestId('volume-icon');
    
    expect(micIcon).toBeInTheDocument();
    expect(messageIcon).toBeInTheDocument();
    expect(volumeIcon).toBeInTheDocument();
  });

  it('should handle missing session gracefully', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} currentSession={null} />
    );
    
    // Should render without errors even without session
    expect(screen.getByText('Processing Pipeline')).toBeInTheDocument();
  });

  it('should show appropriate status badges', () => {
    renderWithProviders(
      <ProcessingPipeline {...defaultProps} stage="processing" />
    );
    
    // Should show status badges for each stage
    const statusBadges = screen.getAllByTestId('stage-status');
    expect(statusBadges.length).toBeGreaterThan(0);
  });

  it('should display progress information', () => {
    renderWithProviders(
      <ProcessingPipeline 
        {...defaultProps} 
        stage="processing"
        currentSession="session_123"
      />
    );
    
    // Should show some form of progress indication
    expect(screen.getByText(/processing/i)).toBeInTheDocument();
  });

  it('should handle all possible stage values', () => {
    const stages = ['idle', 'recording', 'processing', 'completed', 'error'] as const;
    
    stages.forEach(stage => {
      const { rerender } = renderWithProviders(
        <ProcessingPipeline {...defaultProps} stage={stage} />
      );
      
      // Should render without errors for each stage
      expect(screen.getByText('Processing Pipeline')).toBeInTheDocument();
      
      // Clean up for next iteration
      rerender(<div />);
    });
  });
});
