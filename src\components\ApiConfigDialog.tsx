
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Settings, Key, Shield, Globe, Eye, EyeOff, CheckCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ConfigService from '@/services/configService';
import ValidationService from '@/services/validationService';

interface ApiConfigDialogProps {
  trigger: React.ReactNode;
}

interface ValidationErrors {
  deepgram?: string;
  openai?: string;
  elevenlabs?: string;
}

const ApiConfigDialog: React.FC<ApiConfigDialogProps> = ({ trigger }) => {
  const [deepgramKey, setDeepgramKey] = useState('');
  const [openaiKey, setOpenaiKey] = useState('');
  const [elevenlabsKey, setElevenlabsKey] = useState('');
  const [showKeys, setShowKeys] = useState({
    deepgram: false,
    openai: false,
    elevenlabs: false
  });
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const configService = ConfigService.getInstance();

  useEffect(() => {
    if (isOpen) {
      loadExistingConfig();
    }
  }, [isOpen]);

  const loadExistingConfig = () => {
    const config = configService.getConfig();
    setDeepgramKey(config.deepgramApiKey || '');
    setOpenaiKey(config.openaiApiKey || '');
    setElevenlabsKey(config.elevenlabsApiKey || '');
  };

  const validateField = (value: string, service: 'deepgram' | 'openai' | 'elevenlabs'): string | undefined => {
    if (!value.trim()) return undefined;

    const validation = ValidationService.validateApiKey(value, service);
    return validation.isValid ? undefined : validation.error;
  };

  const handleFieldChange = (value: string, field: 'deepgram' | 'openai' | 'elevenlabs') => {
    const error = validateField(value, field);

    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));

    switch (field) {
      case 'deepgram':
        setDeepgramKey(value);
        break;
      case 'openai':
        setOpenaiKey(value);
        break;
      case 'elevenlabs':
        setElevenlabsKey(value);
        break;
    }
  };

  const toggleKeyVisibility = (service: 'deepgram' | 'openai' | 'elevenlabs') => {
    setShowKeys(prev => ({
      ...prev,
      [service]: !prev[service]
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      // Validate all fields
      const errors: ValidationErrors = {};

      if (deepgramKey) {
        const validation = ValidationService.validateApiKey(deepgramKey, 'deepgram');
        if (!validation.isValid) errors.deepgram = validation.error;
      }

      if (openaiKey) {
        const validation = ValidationService.validateApiKey(openaiKey, 'openai');
        if (!validation.isValid) errors.openai = validation.error;
      }

      if (elevenlabsKey) {
        const validation = ValidationService.validateApiKey(elevenlabsKey, 'elevenlabs');
        if (!validation.isValid) errors.elevenlabs = validation.error;
      }

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        toast({
          title: "Validation Error",
          description: "Please fix the validation errors before saving.",
          variant: "destructive"
        });
        return;
      }

      // Save valid keys
      if (deepgramKey) configService.updateApiKey('deepgramApiKey', deepgramKey);
      if (openaiKey) configService.updateApiKey('openaiApiKey', openaiKey);
      if (elevenlabsKey) configService.updateApiKey('elevenlabsApiKey', elevenlabsKey);

      toast({
        title: "API Configuration Saved",
        description: "Your API keys have been securely configured.",
      });

      setIsOpen(false);
    } catch (error) {
      toast({
        title: "Configuration Error",
        description: error instanceof Error ? error.message : "Failed to save configuration",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderKeyInput = (
    service: 'deepgram' | 'openai' | 'elevenlabs',
    value: string,
    placeholder: string,
    icon: React.ReactNode,
    title: string
  ) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <Label htmlFor={`${service}-key`}>API Key</Label>
          <div className="relative">
            <Input
              id={`${service}-key`}
              type={showKeys[service] ? "text" : "password"}
              value={value}
              onChange={(e) => handleFieldChange(e.target.value, service)}
              placeholder={placeholder}
              className={validationErrors[service] ? "border-red-500" : ""}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => toggleKeyVisibility(service)}
            >
              {showKeys[service] ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          {validationErrors[service] && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{validationErrors[service]}</AlertDescription>
            </Alert>
          )}
          {value && !validationErrors[service] && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>Valid API key format</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            API Configuration
          </DialogTitle>
          <DialogDescription>
            Configure your API keys for the voice AI services. Keys are stored securely in your browser's session storage.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {renderKeyInput(
            'deepgram',
            deepgramKey,
            'Enter your Deepgram API key',
            <Globe className="w-4 h-4" />,
            'Deepgram (Speech-to-Text)'
          )}

          {renderKeyInput(
            'openai',
            openaiKey,
            'Enter your OpenAI API key (sk-...)',
            <Key className="w-4 h-4" />,
            'OpenAI (Language Model)'
          )}

          {renderKeyInput(
            'elevenlabs',
            elevenlabsKey,
            'Enter your ElevenLabs API key',
            <Shield className="w-4 h-4" />,
            'ElevenLabs (Text-to-Speech)'
          )}

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              API keys are stored securely in your browser's session storage and are never transmitted to our servers.
              They will be cleared when you close your browser session.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-600"
          >
            {isLoading ? "Saving..." : "Save Configuration"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApiConfigDialog;
