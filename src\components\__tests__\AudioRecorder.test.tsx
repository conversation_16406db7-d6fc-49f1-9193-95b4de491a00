import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockMediaRecorder } from '@/test/utils';
import AudioRecorder from '../AudioRecorder';
import type { AudioRecorderProps } from '@/types';

describe('AudioRecorder', () => {
  const defaultProps: AudioRecorderProps = {
    isRecording: false,
    onStartRecording: vi.fn(),
    onStopRecording: vi.fn(),
    processingStage: 'idle',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render audio recorder component', () => {
    renderWithProviders(<AudioRecorder {...defaultProps} />);
    
    expect(screen.getByText('Audio Input')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('should show start recording button when not recording', () => {
    renderWithProviders(<AudioRecorder {...defaultProps} />);
    
    const button = screen.getByRole('button');
    expect(button).not.toHaveClass('animate-pulse');
    expect(button).not.toBeDisabled();
  });

  it('should show recording state when recording', () => {
    renderWithProviders(
      <AudioRecorder {...defaultProps} isRecording={true} />
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('animate-pulse');
  });

  it('should disable button during processing', () => {
    renderWithProviders(
      <AudioRecorder {...defaultProps} processingStage="processing" />
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('should call onStartRecording when start button is clicked', async () => {
    const user = userEvent.setup();
    const onStartRecording = vi.fn();
    
    renderWithProviders(
      <AudioRecorder {...defaultProps} onStartRecording={onStartRecording} />
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    expect(onStartRecording).toHaveBeenCalledTimes(1);
  });

  it('should not call onStartRecording when button is disabled', async () => {
    const user = userEvent.setup();
    const onStartRecording = vi.fn();
    
    renderWithProviders(
      <AudioRecorder 
        {...defaultProps} 
        onStartRecording={onStartRecording}
        processingStage="processing"
      />
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    expect(onStartRecording).not.toHaveBeenCalled();
  });

  it('should display recording duration when recording', () => {
    renderWithProviders(
      <AudioRecorder {...defaultProps} isRecording={true} />
    );
    
    // The duration should be displayed somewhere in the component
    // This test assumes the component shows duration - adjust based on actual implementation
    expect(screen.getByText(/0:00/)).toBeInTheDocument();
  });

  it('should show audio level visualization when recording', () => {
    renderWithProviders(
      <AudioRecorder {...defaultProps} isRecording={true} />
    );
    
    // Check for audio level indicator - adjust selector based on actual implementation
    const audioLevelIndicator = screen.getByTestId('audio-level-indicator');
    expect(audioLevelIndicator).toBeInTheDocument();
  });

  it('should handle microphone access error gracefully', async () => {
    // Mock getUserMedia to reject
    const mockGetUserMedia = vi.fn().mockRejectedValue(
      new Error('Permission denied')
    );
    Object.defineProperty(navigator, 'mediaDevices', {
      value: { getUserMedia: mockGetUserMedia },
      writable: true,
    });

    const user = userEvent.setup();
    const onStartRecording = vi.fn();
    
    renderWithProviders(
      <AudioRecorder {...defaultProps} onStartRecording={onStartRecording} />
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    // Should handle error gracefully without crashing
    expect(onStartRecording).toHaveBeenCalled();
  });

  it('should clean up resources when component unmounts', () => {
    const { unmount } = renderWithProviders(
      <AudioRecorder {...defaultProps} isRecording={true} />
    );
    
    // Mock the cleanup methods
    const mockStop = vi.fn();
    const mockClose = vi.fn();
    
    // Simulate having active resources
    (global as any).mockMediaRecorder = { stop: mockStop };
    (global as any).mockAudioContext = { close: mockClose };
    
    unmount();
    
    // Verify cleanup would be called (this is more of an integration test)
    // The actual cleanup verification would depend on the component implementation
  });

  it('should update audio level during recording', async () => {
    const { mockRecorder } = mockMediaRecorder();
    
    renderWithProviders(
      <AudioRecorder {...defaultProps} isRecording={true} />
    );
    
    // The component should be monitoring audio levels
    // This test verifies the audio level visualization updates
    await waitFor(() => {
      const audioLevelIndicator = screen.getByTestId('audio-level-indicator');
      expect(audioLevelIndicator).toBeInTheDocument();
    });
  });

  it('should show different states based on processing stage', () => {
    const stages = ['idle', 'recording', 'processing', 'completed'] as const;
    
    stages.forEach(stage => {
      const { rerender } = renderWithProviders(
        <AudioRecorder {...defaultProps} processingStage={stage} />
      );
      
      const button = screen.getByRole('button');
      
      if (stage === 'processing') {
        expect(button).toBeDisabled();
      } else {
        expect(button).not.toBeDisabled();
      }
      
      // Clean up for next iteration
      rerender(<div />);
    });
  });

  it('should handle audio context creation failure', async () => {
    // Mock AudioContext to throw an error
    const originalAudioContext = global.AudioContext;
    global.AudioContext = vi.fn().mockImplementation(() => {
      throw new Error('AudioContext creation failed');
    });

    const user = userEvent.setup();
    const onStartRecording = vi.fn();
    
    renderWithProviders(
      <AudioRecorder {...defaultProps} onStartRecording={onStartRecording} />
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    // Should handle error gracefully
    expect(onStartRecording).toHaveBeenCalled();
    
    // Restore original
    global.AudioContext = originalAudioContext;
  });

  it('should call onStopRecording with audio blob when recording stops', async () => {
    const { mockRecorder, simulateDataAvailable, simulateStop } = mockMediaRecorder();
    const onStopRecording = vi.fn();
    const mockBlob = new Blob(['audio data'], { type: 'audio/wav' });
    
    // Mock MediaRecorder constructor
    global.MediaRecorder = vi.fn().mockImplementation(() => mockRecorder);
    
    renderWithProviders(
      <AudioRecorder 
        {...defaultProps} 
        isRecording={true}
        onStopRecording={onStopRecording}
      />
    );
    
    // Simulate recording process
    simulateDataAvailable(mockBlob);
    simulateStop();
    
    await waitFor(() => {
      expect(onStopRecording).toHaveBeenCalledWith(expect.any(Blob));
    });
  });
});
