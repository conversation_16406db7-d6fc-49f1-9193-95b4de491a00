/**
 * Secure configuration service for managing API keys and environment variables
 */

export interface ApiConfig {
  deepgramApiKey: string;
  openaiApiKey: string;
  elevenlabsApiKey: string;
  apiBaseUrl: string;
  environment: 'development' | 'staging' | 'production';
}

export interface SecureStorage {
  setItem(key: string, value: string): void;
  getItem(key: string): string | null;
  removeItem(key: string): void;
  clear(): void;
}

class ConfigService {
  private static instance: ConfigService;
  private config: Partial<ApiConfig> = {};
  private storage: SecureStorage;

  private constructor() {
    // Use sessionStorage for temporary storage in development
    // In production, this should be replaced with a more secure solution
    this.storage = {
      setItem: (key: string, value: string) => {
        try {
          sessionStorage.setItem(`vapi_${key}`, btoa(value)); // Basic encoding
        } catch (error) {
          console.warn('Failed to store configuration:', error);
        }
      },
      getItem: (key: string) => {
        try {
          const value = sessionStorage.getItem(`vapi_${key}`);
          return value ? atob(value) : null;
        } catch (error) {
          console.warn('Failed to retrieve configuration:', error);
          return null;
        }
      },
      removeItem: (key: string) => {
        try {
          sessionStorage.removeItem(`vapi_${key}`);
        } catch (error) {
          console.warn('Failed to remove configuration:', error);
        }
      },
      clear: () => {
        try {
          Object.keys(sessionStorage)
            .filter(key => key.startsWith('vapi_'))
            .forEach(key => sessionStorage.removeItem(key));
        } catch (error) {
          console.warn('Failed to clear configuration:', error);
        }
      }
    };

    this.loadConfiguration();
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  private loadConfiguration(): void {
    const environment = import.meta.env.VITE_ENVIRONMENT || 'development';
    const isProduction = environment === 'production';

    // In production, API keys are handled by Netlify Functions
    // In development, we can use client-side keys for testing
    this.config = {
      deepgramApiKey: isProduction ? 'PROXY_HANDLED' : (import.meta.env.VITE_DEEPGRAM_API_KEY || this.storage.getItem('deepgram_key') || ''),
      openaiApiKey: isProduction ? 'PROXY_HANDLED' : (import.meta.env.VITE_OPENAI_API_KEY || this.storage.getItem('openai_key') || ''),
      elevenlabsApiKey: isProduction ? 'PROXY_HANDLED' : (import.meta.env.VITE_ELEVENLABS_API_KEY || this.storage.getItem('elevenlabs_key') || ''),
      apiBaseUrl: import.meta.env.VITE_API_BASE_URL || (isProduction ? '/.netlify/functions' : 'http://localhost:8888/.netlify/functions'),
      environment: environment as 'development' | 'staging' | 'production'
    };
  }

  public getConfig(): Partial<ApiConfig> {
    return { ...this.config };
  }

  public updateApiKey(service: keyof Pick<ApiConfig, 'deepgramApiKey' | 'openaiApiKey' | 'elevenlabsApiKey'>, key: string): void {
    if (!this.isValidApiKey(key)) {
      throw new Error('Invalid API key format');
    }

    this.config[service] = key;
    
    // Store in secure storage
    const storageKey = service.replace('ApiKey', '_key');
    this.storage.setItem(storageKey, key);
  }

  public isConfigured(): boolean {
    const isProduction = this.config.environment === 'production';

    if (isProduction) {
      // In production, configuration is always considered valid since API keys are handled server-side
      return true;
    }

    // In development, check for actual API keys
    return !!(this.config.deepgramApiKey &&
              this.config.openaiApiKey &&
              this.config.elevenlabsApiKey &&
              this.config.deepgramApiKey !== 'PROXY_HANDLED' &&
              this.config.openaiApiKey !== 'PROXY_HANDLED' &&
              this.config.elevenlabsApiKey !== 'PROXY_HANDLED');
  }

  public getApiKey(service: keyof Pick<ApiConfig, 'deepgramApiKey' | 'openaiApiKey' | 'elevenlabsApiKey'>): string {
    const key = this.config[service];
    const isProduction = this.config.environment === 'production';

    if (!key) {
      throw new Error(`${service} not configured`);
    }

    if (isProduction && key === 'PROXY_HANDLED') {
      // In production, return a placeholder since actual keys are handled by Netlify Functions
      return 'PROXY_HANDLED';
    }

    return key;
  }

  public clearConfiguration(): void {
    this.config = {
      apiBaseUrl: this.config.apiBaseUrl,
      environment: this.config.environment
    };
    this.storage.clear();
  }

  private isValidApiKey(key: string): boolean {
    // Basic validation - should be enhanced based on each service's key format
    if (!key || typeof key !== 'string') return false;
    if (key.length < 10) return false;
    if (key.includes(' ') || key.includes('\n') || key.includes('\t')) return false;
    
    // Additional validation can be added here for specific API key formats
    return true;
  }

  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!this.config.deepgramApiKey) {
      errors.push('Deepgram API key is required');
    }
    
    if (!this.config.openaiApiKey) {
      errors.push('OpenAI API key is required');
    }
    
    if (!this.config.elevenlabsApiKey) {
      errors.push('ElevenLabs API key is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default ConfigService;
