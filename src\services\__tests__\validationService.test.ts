import { describe, it, expect } from 'vitest';
import ValidationService from '../validationService';

describe('ValidationService', () => {
  describe('sanitizeHtml', () => {
    it('should escape HTML characters', () => {
      const input = '<script>alert("xss")</script>';
      const result = ValidationService.sanitizeHtml(input);
      
      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
    });

    it('should handle empty string', () => {
      const result = ValidationService.sanitizeHtml('');
      expect(result).toBe('');
    });

    it('should handle normal text', () => {
      const input = 'Hello world!';
      const result = ValidationService.sanitizeHtml(input);
      
      expect(result).toBe('Hello world!');
    });
  });

  describe('validateApiKey', () => {
    describe('OpenAI keys', () => {
      it('should validate correct OpenAI API key format', () => {
        const validKey = 'sk-1234567890abcdef1234567890abcdef12345678';
        const result = ValidationService.validateApiKey(validKey, 'openai');
        
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBe(validKey);
        expect(result.error).toBeUndefined();
      });

      it('should reject invalid OpenAI API key format', () => {
        const invalidKey = 'invalid-key';
        const result = ValidationService.validateApiKey(invalidKey, 'openai');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid OpenAI API key format');
      });

      it('should reject OpenAI key without sk- prefix', () => {
        const invalidKey = '1234567890abcdef1234567890abcdef12345678';
        const result = ValidationService.validateApiKey(invalidKey, 'openai');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid OpenAI API key format');
      });
    });

    describe('Deepgram keys', () => {
      it('should validate correct Deepgram API key', () => {
        const validKey = '1234567890abcdef1234567890abcdef12345678';
        const result = ValidationService.validateApiKey(validKey, 'deepgram');
        
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBe(validKey);
      });

      it('should reject short Deepgram API key', () => {
        const shortKey = '12345';
        const result = ValidationService.validateApiKey(shortKey, 'deepgram');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Deepgram API key should be at least 32 characters');
      });
    });

    describe('ElevenLabs keys', () => {
      it('should validate correct ElevenLabs API key', () => {
        const validKey = '1234567890abcdef1234567890abcdef12345678';
        const result = ValidationService.validateApiKey(validKey, 'elevenlabs');
        
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBe(validKey);
      });

      it('should reject short ElevenLabs API key', () => {
        const shortKey = '12345';
        const result = ValidationService.validateApiKey(shortKey, 'elevenlabs');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('ElevenLabs API key should be at least 32 characters');
      });
    });

    it('should handle unknown service', () => {
      const result = ValidationService.validateApiKey('test', 'unknown' as any);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Unknown service');
    });

    it('should trim whitespace from keys', () => {
      const keyWithSpaces = '  sk-1234567890abcdef1234567890abcdef12345678  ';
      const result = ValidationService.validateApiKey(keyWithSpaces, 'openai');
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe('sk-1234567890abcdef1234567890abcdef12345678');
    });
  });

  describe('validateSystemPrompt', () => {
    it('should validate correct system prompt', () => {
      const prompt = 'You are a helpful AI assistant.';
      const result = ValidationService.validateSystemPrompt(prompt);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe(prompt);
    });

    it('should reject empty prompt', () => {
      const result = ValidationService.validateSystemPrompt('');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('System prompt cannot be empty');
    });

    it('should reject whitespace-only prompt', () => {
      const result = ValidationService.validateSystemPrompt('   ');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('System prompt cannot be only whitespace');
    });

    it('should reject overly long prompt', () => {
      const longPrompt = 'a'.repeat(2001);
      const result = ValidationService.validateSystemPrompt(longPrompt);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('System prompt must be less than 2000 characters');
    });

    it('should sanitize HTML in prompt', () => {
      const promptWithHtml = 'You are <script>alert("xss")</script> helpful.';
      const result = ValidationService.validateSystemPrompt(promptWithHtml);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toContain('&lt;script&gt;');
    });
  });

  describe('validateUserInput', () => {
    it('should validate correct user input', () => {
      const input = 'Hello, how are you?';
      const result = ValidationService.validateUserInput(input);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe(input);
    });

    it('should reject overly long input', () => {
      const longInput = 'a'.repeat(1001);
      const result = ValidationService.validateUserInput(longInput);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Input must be less than 1000 characters');
    });

    it('should reject whitespace-only input', () => {
      const result = ValidationService.validateUserInput('   ');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Input cannot be only whitespace');
    });
  });

  describe('validateTool', () => {
    it('should validate correct tool configuration', () => {
      const tool = {
        id: 'test-tool',
        name: 'Test Tool',
        description: 'A test tool',
        webhookUrl: 'https://example.com/webhook',
        parameters: { message: 'string' },
        enabled: true
      };
      
      const result = ValidationService.validateTool(tool);
      
      expect(result.isValid).toBe(true);
      expect(result.validated).toEqual(tool);
    });

    it('should reject tool with invalid webhook URL', () => {
      const tool = {
        id: 'test-tool',
        name: 'Test Tool',
        description: 'A test tool',
        webhookUrl: 'http://example.com/webhook', // HTTP instead of HTTPS
        parameters: { message: 'string' },
        enabled: true
      };
      
      const result = ValidationService.validateTool(tool);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('HTTPS');
    });

    it('should reject tool with invalid name characters', () => {
      const tool = {
        id: 'test-tool',
        name: 'Test Tool @#$%', // Invalid characters
        description: 'A test tool',
        webhookUrl: 'https://example.com/webhook',
        parameters: { message: 'string' },
        enabled: true
      };
      
      const result = ValidationService.validateTool(tool);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('invalid characters');
    });
  });

  describe('validateAudioFile', () => {
    it('should validate correct audio file', () => {
      const audioFile = new File(['audio data'], 'test.wav', { type: 'audio/wav' });
      const result = ValidationService.validateAudioFile(audioFile);
      
      expect(result.isValid).toBe(true);
    });

    it('should reject non-audio file', () => {
      const textFile = new File(['text data'], 'test.txt', { type: 'text/plain' });
      const result = ValidationService.validateAudioFile(textFile);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid audio file type');
    });

    it('should reject oversized file', () => {
      // Create a mock file that appears to be over 10MB
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.wav', { type: 'audio/wav' });
      const result = ValidationService.validateAudioFile(largeFile);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('must be less than 10MB');
    });
  });

  describe('validateWebhookUrl', () => {
    it('should validate correct HTTPS URL', () => {
      const url = 'https://example.com/webhook';
      const result = ValidationService.validateWebhookUrl(url);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe(url);
    });

    it('should reject HTTP URL', () => {
      const url = 'http://example.com/webhook';
      const result = ValidationService.validateWebhookUrl(url);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid HTTPS URL');
    });

    it('should reject invalid URL format', () => {
      const url = 'not-a-url';
      const result = ValidationService.validateWebhookUrl(url);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid HTTPS URL');
    });

    it('should trim whitespace from URL', () => {
      const url = '  https://example.com/webhook  ';
      const result = ValidationService.validateWebhookUrl(url);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe('https://example.com/webhook');
    });
  });
});
