/**
 * Comprehensive Model Registry Service
 * Manages all available models for STT, LLM, and TTS providers
 */

export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  provider: string;
  category: 'stt' | 'llm' | 'tts';
  capabilities?: string[];
  languages?: string[];
  pricing?: 'free' | 'paid' | 'premium';
  maxTokens?: number;
  contextWindow?: number;
  isRecommended?: boolean;
  isNew?: boolean;
  deprecated?: boolean;
}

export interface VoiceInfo {
  id: string;
  name: string;
  provider: string;
  gender: 'male' | 'female' | 'neutral';
  accent?: string;
  language: string;
  description?: string;
  isRecommended?: boolean;
  isNew?: boolean;
  category?: 'standard' | 'premium' | 'cloned';
}

class ModelRegistryService {
  private static instance: ModelRegistryService;
  
  private constructor() {}

  public static getInstance(): ModelRegistryService {
    if (!ModelRegistryService.instance) {
      ModelRegistryService.instance = new ModelRegistryService();
    }
    return ModelRegistryService.instance;
  }

  // STT Models
  public getSTTModels(): ModelInfo[] {
    return [
      // Deepgram Models
      {
        id: 'nova-2',
        name: 'Nova 2',
        description: 'Latest and most accurate general-purpose model',
        provider: 'deepgram',
        category: 'stt',
        capabilities: ['real-time', 'batch', 'streaming'],
        languages: ['en', 'es', 'fr', 'de', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'nova',
        name: 'Nova',
        description: 'Previous generation general-purpose model',
        provider: 'deepgram',
        category: 'stt',
        capabilities: ['real-time', 'batch', 'streaming'],
        languages: ['en', 'es', 'fr', 'de', 'pt'],
        pricing: 'paid'
      },
      {
        id: 'enhanced',
        name: 'Enhanced',
        description: 'Higher accuracy for challenging audio',
        provider: 'deepgram',
        category: 'stt',
        capabilities: ['batch'],
        languages: ['en'],
        pricing: 'premium'
      },
      {
        id: 'base',
        name: 'Base',
        description: 'Cost-effective option for clear audio',
        provider: 'deepgram',
        category: 'stt',
        capabilities: ['real-time', 'batch'],
        languages: ['en'],
        pricing: 'paid'
      },
      
      // OpenAI Models
      {
        id: 'whisper-1',
        name: 'Whisper',
        description: 'OpenAI\'s robust speech recognition model',
        provider: 'openai',
        category: 'stt',
        capabilities: ['batch', 'translation'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi'],
        pricing: 'paid',
        isRecommended: true
      },
      
      // AssemblyAI Models
      {
        id: 'best',
        name: 'Best',
        description: 'Highest accuracy model with advanced features',
        provider: 'assemblyai',
        category: 'stt',
        capabilities: ['real-time', 'batch', 'speaker-diarization', 'sentiment'],
        languages: ['en'],
        pricing: 'premium',
        isRecommended: true
      },
      {
        id: 'nano',
        name: 'Nano',
        description: 'Fast and cost-effective model',
        provider: 'assemblyai',
        category: 'stt',
        capabilities: ['real-time', 'batch'],
        languages: ['en'],
        pricing: 'paid'
      },
      
      // Google Speech Models
      {
        id: 'latest_long',
        name: 'Latest Long',
        description: 'Optimized for long-form audio content',
        provider: 'google',
        category: 'stt',
        capabilities: ['batch'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid'
      },
      {
        id: 'latest_short',
        name: 'Latest Short',
        description: 'Optimized for short audio clips',
        provider: 'google',
        category: 'stt',
        capabilities: ['real-time', 'batch'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid',
        isRecommended: true
      },
      {
        id: 'chirp',
        name: 'Chirp',
        description: 'Universal speech model with broad language support',
        provider: 'google',
        category: 'stt',
        capabilities: ['batch'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi', 'th', 'vi'],
        pricing: 'premium',
        isNew: true
      }
    ];
  }

  // LLM Models
  public getLLMModels(): ModelInfo[] {
    return [
      // OpenAI Models
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: 'Most advanced multimodal model with vision capabilities',
        provider: 'openai',
        category: 'llm',
        capabilities: ['text', 'vision', 'function-calling'],
        maxTokens: 4096,
        contextWindow: 128000,
        pricing: 'premium',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: 'Latest GPT-4 with improved speed and efficiency',
        provider: 'openai',
        category: 'llm',
        capabilities: ['text', 'function-calling'],
        maxTokens: 4096,
        contextWindow: 128000,
        pricing: 'premium',
        isRecommended: true
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'Most capable model for complex reasoning tasks',
        provider: 'openai',
        category: 'llm',
        capabilities: ['text', 'function-calling'],
        maxTokens: 4096,
        contextWindow: 8192,
        pricing: 'premium'
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and cost-effective for most tasks',
        provider: 'openai',
        category: 'llm',
        capabilities: ['text', 'function-calling'],
        maxTokens: 4096,
        contextWindow: 16385,
        pricing: 'paid'
      },
      
      // Anthropic Models
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        description: 'Most intelligent model with enhanced reasoning',
        provider: 'anthropic',
        category: 'llm',
        capabilities: ['text', 'vision', 'function-calling'],
        maxTokens: 8192,
        contextWindow: 200000,
        pricing: 'premium',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        description: 'Most powerful model for complex tasks',
        provider: 'anthropic',
        category: 'llm',
        capabilities: ['text', 'vision'],
        maxTokens: 4096,
        contextWindow: 200000,
        pricing: 'premium'
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        description: 'Balanced performance and speed',
        provider: 'anthropic',
        category: 'llm',
        capabilities: ['text', 'vision'],
        maxTokens: 4096,
        contextWindow: 200000,
        pricing: 'paid',
        isRecommended: true
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        description: 'Fastest model for simple tasks',
        provider: 'anthropic',
        category: 'llm',
        capabilities: ['text', 'vision'],
        maxTokens: 4096,
        contextWindow: 200000,
        pricing: 'paid'
      },
      
      // Google Models
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: 'Advanced multimodal model with large context',
        provider: 'google',
        category: 'llm',
        capabilities: ['text', 'vision', 'function-calling'],
        maxTokens: 8192,
        contextWindow: 2000000,
        pricing: 'premium',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: 'Fast and efficient multimodal model',
        provider: 'google',
        category: 'llm',
        capabilities: ['text', 'vision', 'function-calling'],
        maxTokens: 8192,
        contextWindow: 1000000,
        pricing: 'paid',
        isRecommended: true
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: 'Powerful text-only model',
        provider: 'google',
        category: 'llm',
        capabilities: ['text'],
        maxTokens: 2048,
        contextWindow: 32768,
        pricing: 'paid'
      },
      {
        id: 'gemini-pro-vision',
        name: 'Gemini Pro Vision',
        description: 'Multimodal model with vision capabilities',
        provider: 'google',
        category: 'llm',
        capabilities: ['text', 'vision'],
        maxTokens: 2048,
        contextWindow: 16384,
        pricing: 'paid'
      }
    ];
  }

  // TTS Models
  public getTTSModels(): ModelInfo[] {
    return [
      // ElevenLabs Models
      {
        id: 'eleven_multilingual_v2',
        name: 'Multilingual v2',
        description: 'High-quality multilingual text-to-speech',
        provider: 'elevenlabs',
        category: 'tts',
        capabilities: ['multilingual', 'voice-cloning'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'pl', 'hi', 'ar'],
        pricing: 'premium',
        isRecommended: true
      },
      {
        id: 'eleven_turbo_v2_5',
        name: 'Turbo v2.5',
        description: 'Fastest model with excellent quality',
        provider: 'elevenlabs',
        category: 'tts',
        capabilities: ['fast-generation', 'streaming'],
        languages: ['en'],
        pricing: 'paid',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'eleven_turbo_v2',
        name: 'Turbo v2',
        description: 'Fast generation with good quality',
        provider: 'elevenlabs',
        category: 'tts',
        capabilities: ['fast-generation'],
        languages: ['en'],
        pricing: 'paid'
      },
      {
        id: 'eleven_monolingual_v1',
        name: 'Monolingual v1',
        description: 'High-quality English-only model',
        provider: 'elevenlabs',
        category: 'tts',
        capabilities: ['high-quality'],
        languages: ['en'],
        pricing: 'paid'
      },

      // OpenAI Models
      {
        id: 'tts-1-hd',
        name: 'TTS-1 HD',
        description: 'High-definition text-to-speech model',
        provider: 'openai',
        category: 'tts',
        capabilities: ['high-quality'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'premium',
        isRecommended: true
      },
      {
        id: 'tts-1',
        name: 'TTS-1',
        description: 'Standard text-to-speech model',
        provider: 'openai',
        category: 'tts',
        capabilities: ['standard-quality'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid'
      },

      // Google Cloud Models
      {
        id: 'neural2',
        name: 'Neural2',
        description: 'Latest neural text-to-speech technology',
        provider: 'google',
        category: 'tts',
        capabilities: ['neural-synthesis', 'ssml'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'premium',
        isRecommended: true,
        isNew: true
      },
      {
        id: 'wavenet',
        name: 'WaveNet',
        description: 'High-quality neural synthesis',
        provider: 'google',
        category: 'tts',
        capabilities: ['neural-synthesis', 'ssml'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid'
      },
      {
        id: 'standard',
        name: 'Standard',
        description: 'Cost-effective concatenative synthesis',
        provider: 'google',
        category: 'tts',
        capabilities: ['ssml'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid'
      },

      // Azure Speech Models
      {
        id: 'neural',
        name: 'Neural',
        description: 'High-quality neural voices',
        provider: 'azure',
        category: 'tts',
        capabilities: ['neural-synthesis', 'ssml', 'custom-voices'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid',
        isRecommended: true
      },
      {
        id: 'standard',
        name: 'Standard',
        description: 'Traditional concatenative synthesis',
        provider: 'azure',
        category: 'tts',
        capabilities: ['ssml'],
        languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        pricing: 'paid'
      }
    ];
  }

  // Voice Registry
  public getVoices(): VoiceInfo[] {
    return [
      // ElevenLabs Voices
      {
        id: '9BWtsMINqrJLrRacOk9x',
        name: 'Aria',
        provider: 'elevenlabs',
        gender: 'female',
        language: 'en',
        accent: 'American',
        description: 'Warm and expressive female voice',
        isRecommended: true,
        category: 'premium'
      },
      {
        id: 'CwhRBWXzGAHq8TQ4Fs17',
        name: 'Roger',
        provider: 'elevenlabs',
        gender: 'male',
        language: 'en',
        accent: 'American',
        description: 'Professional male voice',
        category: 'premium'
      },
      {
        id: 'EXAVITQu4vr4xnSDxMaL',
        name: 'Sarah',
        provider: 'elevenlabs',
        gender: 'female',
        language: 'en',
        accent: 'American',
        description: 'Clear and articulate female voice',
        category: 'premium'
      },
      {
        id: 'FGY2WhTYpPnrIDTdsKH5',
        name: 'Laura',
        provider: 'elevenlabs',
        gender: 'female',
        language: 'en',
        accent: 'American',
        description: 'Friendly and approachable female voice',
        category: 'premium'
      },
      {
        id: 'IKne3meq5aSn9XLyUdCD',
        name: 'Charlie',
        provider: 'elevenlabs',
        gender: 'male',
        language: 'en',
        accent: 'Australian',
        description: 'Casual and relaxed male voice',
        category: 'premium'
      },
      {
        id: 'JBFqnCBsd6RMkjVDRZzb',
        name: 'George',
        provider: 'elevenlabs',
        gender: 'male',
        language: 'en',
        accent: 'British',
        description: 'Distinguished British male voice',
        category: 'premium'
      },
      {
        id: 'N2lVS1w4EtoT3dr4eOWO',
        name: 'Callum',
        provider: 'elevenlabs',
        gender: 'male',
        language: 'en',
        accent: 'American',
        description: 'Young and energetic male voice',
        category: 'premium'
      },
      {
        id: 'SAz9YHcvj6GT2YYXdXww',
        name: 'River',
        provider: 'elevenlabs',
        gender: 'neutral',
        language: 'en',
        accent: 'American',
        description: 'Versatile gender-neutral voice',
        category: 'premium'
      },

      // OpenAI Voices
      {
        id: 'alloy',
        name: 'Alloy',
        provider: 'openai',
        gender: 'neutral',
        language: 'en',
        description: 'Balanced and versatile voice',
        isRecommended: true,
        category: 'standard'
      },
      {
        id: 'echo',
        name: 'Echo',
        provider: 'openai',
        gender: 'male',
        language: 'en',
        description: 'Clear and resonant male voice',
        category: 'standard'
      },
      {
        id: 'fable',
        name: 'Fable',
        provider: 'openai',
        gender: 'male',
        language: 'en',
        description: 'Expressive storytelling voice',
        category: 'standard'
      },
      {
        id: 'onyx',
        name: 'Onyx',
        provider: 'openai',
        gender: 'male',
        language: 'en',
        description: 'Deep and authoritative male voice',
        category: 'standard'
      },
      {
        id: 'nova',
        name: 'Nova',
        provider: 'openai',
        gender: 'female',
        language: 'en',
        description: 'Bright and energetic female voice',
        isRecommended: true,
        category: 'standard'
      },
      {
        id: 'shimmer',
        name: 'Shimmer',
        provider: 'openai',
        gender: 'female',
        language: 'en',
        description: 'Soft and gentle female voice',
        category: 'standard'
      }
    ];
  }

  // Utility methods
  public getModelsByProvider(provider: string, category?: 'stt' | 'llm' | 'tts'): ModelInfo[] {
    let models: ModelInfo[] = [];

    switch (category) {
      case 'stt':
        models = this.getSTTModels();
        break;
      case 'llm':
        models = this.getLLMModels();
        break;
      case 'tts':
        models = this.getTTSModels();
        break;
      default:
        models = [...this.getSTTModels(), ...this.getLLMModels(), ...this.getTTSModels()];
    }

    return models.filter(model => model.provider === provider);
  }

  public getRecommendedModels(category?: 'stt' | 'llm' | 'tts'): ModelInfo[] {
    let models: ModelInfo[] = [];

    switch (category) {
      case 'stt':
        models = this.getSTTModels();
        break;
      case 'llm':
        models = this.getLLMModels();
        break;
      case 'tts':
        models = this.getTTSModels();
        break;
      default:
        models = [...this.getSTTModels(), ...this.getLLMModels(), ...this.getTTSModels()];
    }

    return models.filter(model => model.isRecommended);
  }

  public getVoicesByProvider(provider: string): VoiceInfo[] {
    return this.getVoices().filter(voice => voice.provider === provider);
  }

  public getAvailableProviders(category?: 'stt' | 'llm' | 'tts'): string[] {
    let models: ModelInfo[] = [];

    switch (category) {
      case 'stt':
        models = this.getSTTModels();
        break;
      case 'llm':
        models = this.getLLMModels();
        break;
      case 'tts':
        models = this.getTTSModels();
        break;
      default:
        models = [...this.getSTTModels(), ...this.getLLMModels(), ...this.getTTSModels()];
    }

    return [...new Set(models.map(model => model.provider))];
  }
}

export default ModelRegistryService;
