
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mic, MessageSquare, Volume2, CheckCircle, Clock, AlertCircle, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProcessingPipelineProps {
  stage: string;
  currentSession: string | null;
}

const ProcessingPipeline: React.FC<ProcessingPipelineProps> = ({
  stage,
  currentSession
}) => {
  const getStageStatus = (stageName: string) => {
    if (stage === 'idle') return 'pending';
    if (stage === 'recording' && stageName === 'stt') return 'active';
    if (stage === 'processing') {
      if (stageName === 'stt') return 'completed';
      if (stageName === 'llm') return 'active';
      if (stageName === 'tts') return 'pending';
    }
    if (stage === 'completed') return 'completed';
    return 'pending';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'active':
        return <Clock className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200';
      case 'active':
        return 'bg-blue-50 border-blue-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const stages = [
    {
      id: 'stt',
      name: 'Speech-to-Text',
      description: 'Deepgram transcription',
      icon: Mic,
      status: getStageStatus('stt')
    },
    {
      id: 'llm',
      name: 'Language Model',
      description: 'OpenAI GPT-4 processing',
      icon: MessageSquare,
      status: getStageStatus('llm')
    },
    {
      id: 'tts',
      name: 'Text-to-Speech',
      description: 'ElevenLabs synthesis',
      icon: Volume2,
      status: getStageStatus('tts')
    }
  ];

  return (
    <Card className="bg-white/70 backdrop-blur-sm border-white/20 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg text-gray-800 flex items-center gap-2">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
            <Settings className="w-4 h-4 text-white" />
          </div>
          Processing Pipeline
        </CardTitle>
        {currentSession && (
          <Badge variant="outline" className="w-fit bg-white/50">
            Session: {currentSession}
          </Badge>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stages.map((stageItem, index) => {
            const Icon = stageItem.icon;
            return (
              <div key={stageItem.id} className="flex items-center gap-4">
                {/* Stage Number */}
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-gray-100 to-gray-200 flex items-center justify-center text-sm font-semibold text-gray-700 border border-gray-300">
                  {index + 1}
                </div>
                
                {/* Stage Card */}
                <Card className={cn(
                  "flex-1 transition-all duration-300 shadow-sm",
                  getStatusColor(stageItem.status)
                )}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className="w-5 h-5 text-gray-700" />
                        <div>
                          <div className="font-semibold text-gray-800">{stageItem.name}</div>
                          <div className="text-sm text-gray-600">{stageItem.description}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {getStatusIcon(stageItem.status)}
                        <Badge 
                          variant={stageItem.status === 'completed' ? 'default' : 'secondary'}
                          className={cn(
                            "text-xs",
                            stageItem.status === 'active' && "animate-pulse bg-blue-100 text-blue-800",
                            stageItem.status === 'completed' && "bg-green-100 text-green-800"
                          )}
                        >
                          {stageItem.status}
                        </Badge>
                      </div>
                    </div>
                    
                    {/* Progress indicator for active stage */}
                    {stageItem.status === 'active' && (
                      <div className="mt-3 w-full bg-gray-200 h-1 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-blue-400 to-blue-500 animate-pulse" style={{ width: '60%' }} />
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {/* Connection Line */}
                {index < stages.length - 1 && (
                  <div className="flex-shrink-0 w-px h-8 bg-gray-300" />
                )}
              </div>
            );
          })}
        </div>
        
        {/* Performance Metrics */}
        {stage === 'completed' && (
          <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
            <h4 className="font-semibold mb-2 text-gray-800">Performance Metrics</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-gray-600">STT Latency</div>
                <div className="font-semibold text-gray-800">1.2s</div>
              </div>
              <div>
                <div className="text-gray-600">LLM Processing</div>
                <div className="font-semibold text-gray-800">2.8s</div>
              </div>
              <div>
                <div className="text-gray-600">TTS Generation</div>
                <div className="font-semibold text-gray-800">1.5s</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProcessingPipeline;
