
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Play, Download, Volume2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const TTSTester = () => {
  const [text, setText] = useState('');
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [provider, setProvider] = useState('elevenlabs');
  const [voice, setVoice] = useState('Aria - 9BWtsMINqrJLrRacOk9x');
  const [customVoiceId, setCustomVoiceId] = useState('');
  const [useCustomVoice, setUseCustomVoice] = useState(false);
  const [model, setModel] = useState('eleven_multilingual_v2');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const providers = {
    elevenlabs: {
      models: ['eleven_multilingual_v2', 'eleven_turbo_v2_5', 'eleven_turbo_v2'],
      voices: [
        'Aria - 9BWtsMINqrJLrRacOk9x',
        'Roger - CwhRBWXzGAHq8TQ4Fs17',
        'Sarah - EXAVITQu4vr4xnSDxMaL',
        'Laura - FGY2WhTYpPnrIDTdsKH5',
        'Charlie - IKne3meq5aSn9XLyUdCD',
        'George - JBFqnCBsd6RMkjVDRZzb',
        'Callum - N2lVS1w4EtoT3dr4eOWO',
        'River - SAz9YHcvj6GT2YYXdXww'
      ]
    },
    openai: {
      models: ['tts-1', 'tts-1-hd'],
      voices: ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']
    },
    google: {
      models: ['standard', 'wavenet', 'neural2'],
      voices: ['en-US-Standard-A', 'en-US-Standard-B', 'en-US-Wavenet-A']
    }
  };

  const handleGenerate = async () => {
    if (!text.trim()) return;
    
    setIsProcessing(true);
    // Simulate TTS processing
    setTimeout(() => {
      // Create a fake audio blob for demo
      const fakeAudio = new Blob(['fake audio data'], { type: 'audio/mp3' });
      setAudioBlob(fakeAudio);
      setIsProcessing(false);
      toast({
        title: "Audio Generated",
        description: `Generated with ${provider} (${model})`
      });
    }, 2000);
  };

  const handlePlayAudio = () => {
    if (audioBlob) {
      // In a real implementation, this would play the actual audio
      toast({
        title: "Playing Audio",
        description: "Audio playback would start here"
      });
    }
  };

  const handleDownloadAudio = () => {
    if (audioBlob) {
      const url = URL.createObjectURL(audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tts-output-${Date.now()}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const quickTexts = [
    "Hello, this is a test of the text-to-speech system.",
    "The quick brown fox jumps over the lazy dog.",
    "Welcome to our voice AI testing framework. How may I assist you today?",
    "Thank you for using our service. Have a wonderful day!"
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-2xl text-green-900 flex items-center gap-2">
            <Volume2 className="w-6 h-6" />
            Text-to-Speech Testing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Provider Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>TTS Provider</Label>
              <Select value={provider} onValueChange={(value) => {
                setProvider(value);
                setModel(providers[value as keyof typeof providers].models[0]);
                setVoice(providers[value as keyof typeof providers].voices[0]);
              }}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="google">Google Cloud</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Model</Label>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {providers[provider as keyof typeof providers].models.map((m) => (
                    <SelectItem key={m} value={m}>{m}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Voice Selection */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Voice Selection</Label>
              <Select 
                value={useCustomVoice ? 'custom' : voice} 
                onValueChange={(value) => {
                  if (value === 'custom') {
                    setUseCustomVoice(true);
                  } else {
                    setUseCustomVoice(false);
                    setVoice(value);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {providers[provider as keyof typeof providers].voices.map((v) => (
                    <SelectItem key={v} value={v}>{v}</SelectItem>
                  ))}
                  <SelectItem value="custom">Custom Voice ID</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {useCustomVoice && (
              <div className="space-y-2">
                <Label>Custom Voice ID</Label>
                <Input
                  value={customVoiceId}
                  onChange={(e) => setCustomVoiceId(e.target.value)}
                  placeholder="Enter voice ID (e.g., 9BWtsMINqrJLrRacOk9x)"
                />
              </div>
            )}
          </div>

          {/* Text Input */}
          <div className="space-y-2">
            <Label>Text to Convert</Label>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter the text you want to convert to speech..."
              className="min-h-[120px] bg-white"
            />
            <div className="flex flex-wrap gap-2">
              {quickTexts.map((quickText, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setText(quickText)}
                  className="text-xs"
                >
                  Sample {index + 1}
                </Button>
              ))}
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleGenerate}
              disabled={isProcessing || !text.trim()}
              size="lg"
              className="bg-green-500 hover:bg-green-600"
            >
              <Volume2 className="w-4 h-4 mr-2" />
              {isProcessing ? 'Generating...' : 'Generate Speech'}
            </Button>
          </div>

          {/* Audio Controls */}
          {audioBlob && (
            <div className="flex justify-center space-x-4 pt-4 border-t">
              <Button variant="outline" onClick={handlePlayAudio}>
                <Play className="w-4 h-4 mr-2" />
                Play Audio
              </Button>
              <Button variant="outline" onClick={handleDownloadAudio}>
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TTSTester;
