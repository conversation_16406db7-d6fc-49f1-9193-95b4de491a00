import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { AppProvider } from '@/context/AppContext';
import { TooltipProvider } from '@/components/ui/tooltip';
import type { AppState, Tool, SessionLog } from '@/types';

// Create a custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: Partial<AppState>;
  queryClient?: QueryClient;
}

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

export function renderWithProviders(
  ui: ReactElement,
  {
    initialState,
    queryClient = createTestQueryClient(),
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AppProvider>
            <TooltipProvider>
              {children}
            </TooltipProvider>
          </AppProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  };
}

// Mock data factories
export const createMockTool = (overrides: Partial<Tool> = {}): Tool => ({
  id: 'test-tool-1',
  name: 'Test Tool',
  description: 'A test tool for testing',
  webhookUrl: 'https://example.com/webhook',
  parameters: {
    message: 'string',
    count: 'number',
  },
  enabled: true,
  ...overrides,
});

export const createMockSessionLog = (overrides: Partial<SessionLog> = {}): SessionLog => ({
  id: 'test-session-1',
  timestamp: '2023-01-01T00:00:00.000Z',
  inputAudio: new Blob(['test audio'], { type: 'audio/wav' }),
  transcript: 'Test transcript',
  gptResponse: 'Test response',
  ttsAudio: new Blob(['test tts audio'], { type: 'audio/wav' }),
  status: 'completed',
  ...overrides,
});

export const createMockAppState = (overrides: Partial<AppState> = {}): AppState => ({
  session: {
    current: null,
    logs: [],
    isRecording: false,
    processingStage: 'idle',
  },
  configuration: {
    api: {
      environment: 'test',
    },
    defaultModels: {
      stt: {
        provider: 'deepgram',
        model: 'nova-2',
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4',
      },
      tts: {
        provider: 'elevenlabs',
        model: 'eleven_multilingual_v2',
        voice: 'test-voice',
      },
    },
    ui: {
      theme: 'system',
      language: 'en',
    },
  },
  tools: [],
  ui: {
    activeTab: 'full-pipeline',
    isConfigDialogOpen: false,
    errors: [],
  },
  ...overrides,
});

// Test helpers
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0));

export const mockFetch = (response: any, ok: boolean = true) => {
  global.fetch = vi.fn().mockResolvedValue({
    ok,
    status: ok ? 200 : 400,
    json: async () => response,
    text: async () => JSON.stringify(response),
  });
};

export const mockFetchError = (error: Error) => {
  global.fetch = vi.fn().mockRejectedValue(error);
};

// Audio testing utilities
export const createMockAudioBlob = (duration: number = 1000): Blob => {
  return new Blob(['mock audio data'], { type: 'audio/wav' });
};

export const mockMediaRecorder = () => {
  const mockRecorder = {
    start: vi.fn(),
    stop: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    state: 'inactive',
    ondataavailable: null as ((event: any) => void) | null,
    onstop: null as (() => void) | null,
    onerror: null as ((event: any) => void) | null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  };

  // Simulate data available event
  const simulateDataAvailable = (data: Blob) => {
    if (mockRecorder.ondataavailable) {
      mockRecorder.ondataavailable({ data });
    }
  };

  // Simulate stop event
  const simulateStop = () => {
    mockRecorder.state = 'inactive';
    if (mockRecorder.onstop) {
      mockRecorder.onstop();
    }
  };

  return {
    mockRecorder,
    simulateDataAvailable,
    simulateStop,
  };
};

// Error testing utilities
export const mockConsoleError = () => {
  const originalError = console.error;
  console.error = vi.fn();
  
  return () => {
    console.error = originalError;
  };
};

export const expectToastMessage = async (message: string) => {
  // This would need to be implemented based on your toast library
  // For now, it's a placeholder
  await waitForLoadingToFinish();
};

// Custom matchers (if needed)
export const customMatchers = {
  toBeValidAudioBlob: (received: any) => {
    const pass = received instanceof Blob && received.type.startsWith('audio/');
    return {
      message: () => `expected ${received} to be a valid audio Blob`,
      pass,
    };
  },
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
