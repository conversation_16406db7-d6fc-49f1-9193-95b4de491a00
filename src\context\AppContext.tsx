import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import type { AppState, AppAction, SessionLog, ProcessingStage, Tool, AppError } from '@/types';

// Initial state
const initialState: AppState = {
  session: {
    current: null,
    logs: [],
    isRecording: false,
    processingStage: 'idle'
  },
  configuration: {
    api: {
      environment: 'development'
    },
    defaultModels: {
      stt: {
        provider: 'deepgram',
        model: 'nova-2'
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4'
      },
      tts: {
        provider: 'elevenlabs',
        model: 'eleven_multilingual_v2',
        voice: 'Aria - 9BWtsMINqrJLrRacOk9x'
      }
    },
    ui: {
      theme: 'system',
      language: 'en'
    }
  },
  tools: [],
  ui: {
    activeTab: 'full-pipeline',
    isConfigDialogOpen: false,
    errors: []
  }
};

// Reducer function
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SESSION_START':
      return {
        ...state,
        session: {
          ...state.session,
          current: action.payload.sessionId,
          isRecording: true,
          processingStage: 'recording'
        }
      };

    case 'SESSION_END':
      return {
        ...state,
        session: {
          ...state.session,
          current: null,
          logs: [action.payload.sessionLog, ...state.session.logs],
          isRecording: false,
          processingStage: 'completed'
        }
      };

    case 'RECORDING_START':
      return {
        ...state,
        session: {
          ...state.session,
          isRecording: true,
          processingStage: 'recording'
        }
      };

    case 'RECORDING_STOP':
      return {
        ...state,
        session: {
          ...state.session,
          isRecording: false,
          processingStage: 'processing'
        }
      };

    case 'PROCESSING_STAGE_CHANGE':
      return {
        ...state,
        session: {
          ...state.session,
          processingStage: action.payload.stage
        }
      };

    case 'CONFIG_UPDATE':
      return {
        ...state,
        configuration: {
          ...state.configuration,
          ...action.payload.config
        }
      };

    case 'TOOLS_UPDATE':
      return {
        ...state,
        tools: action.payload.tools
      };

    case 'ERROR_ADD':
      return {
        ...state,
        ui: {
          ...state.ui,
          errors: [...state.ui.errors, action.payload.error]
        }
      };

    case 'ERROR_CLEAR':
      return {
        ...state,
        ui: {
          ...state.ui,
          errors: action.payload.errorId
            ? state.ui.errors.filter(error => error.code !== action.payload.errorId)
            : []
        }
      };

    case 'UI_TAB_CHANGE':
      return {
        ...state,
        ui: {
          ...state.ui,
          activeTab: action.payload.tab
        }
      };

    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    startSession: (sessionId: string) => void;
    endSession: (sessionLog: SessionLog) => void;
    startRecording: () => void;
    stopRecording: (audioBlob: Blob) => void;
    setProcessingStage: (stage: ProcessingStage) => void;
    updateConfiguration: (config: Partial<AppState['configuration']>) => void;
    updateTools: (tools: Tool[]) => void;
    addError: (error: AppError) => void;
    clearErrors: (errorId?: string) => void;
    changeTab: (tab: string) => void;
  };
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const actions = {
    startSession: (sessionId: string) => {
      dispatch({ type: 'SESSION_START', payload: { sessionId } });
    },

    endSession: (sessionLog: SessionLog) => {
      dispatch({ type: 'SESSION_END', payload: { sessionLog } });
    },

    startRecording: () => {
      dispatch({ type: 'RECORDING_START' });
    },

    stopRecording: (audioBlob: Blob) => {
      dispatch({ type: 'RECORDING_STOP', payload: { audioBlob } });
    },

    setProcessingStage: (stage: ProcessingStage) => {
      dispatch({ type: 'PROCESSING_STAGE_CHANGE', payload: { stage } });
    },

    updateConfiguration: (config: Partial<AppState['configuration']>) => {
      dispatch({ type: 'CONFIG_UPDATE', payload: { config } });
    },

    updateTools: (tools: Tool[]) => {
      dispatch({ type: 'TOOLS_UPDATE', payload: { tools } });
    },

    addError: (error: AppError) => {
      dispatch({ type: 'ERROR_ADD', payload: { error } });
    },

    clearErrors: (errorId?: string) => {
      dispatch({ type: 'ERROR_CLEAR', payload: { errorId } });
    },

    changeTab: (tab: string) => {
      dispatch({ type: 'UI_TAB_CHANGE', payload: { tab } });
    }
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    actions
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

// Selector hooks for specific parts of state
export const useSession = () => {
  const { state } = useAppContext();
  return state.session;
};

export const useConfiguration = () => {
  const { state } = useAppContext();
  return state.configuration;
};

export const useTools = () => {
  const { state } = useAppContext();
  return state.tools;
};

export const useUIState = () => {
  const { state } = useAppContext();
  return state.ui;
};

export default AppContext;
